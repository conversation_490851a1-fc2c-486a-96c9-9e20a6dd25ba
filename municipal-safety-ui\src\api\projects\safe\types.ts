export interface SafeTaskVO {
  /**
   * 主键
   */
  openTaskId: string | number;

  /**
   * 项目名称
   */
  projectName: string;

  /**
   * 项目地址
   */
  projectAddress: string;

  /**
   * 项目经度
   */
  projectLongitude: string;

  /**
   * 项目维度
   */
  projectLatitude: string;

  /**
   * 塔机现场编号
   */
  projectCraneNum: string;

  /**
   * 塔机类型
   平臂:FlatArm
   动臂:SwingArm
   塔头:Tower
   缺少该字段默认传：
   FlatArm
   */
  craneType: string;

  /**
   * 塔机规格型号
   */
  craneModel: string;

  /**
   * 塔机出厂编号
   */
  craneSn: string;

  /**
   * 塔机出厂日期
   */
  craneProductionDate: string;

  /**
   * 产权单位名称
   */
  propertyCompanyName: string;

  /**
   * 塔机生产厂商名称
   */
  factoryName: string;

  /**
   * 顶升降节作业类型
   顶升:JackingUp
   降节:Disassembly
   */
  jackingType: string;

  /**
   * 执行日期
   */
  executioinDate: string;

  /**
   * 升/降节数
   */
  sectionNum: number;

  /**
   * 加降节后
   塔机高度
   */
  modifiedCraneHeight: number;

  /**
   * 加降节前
   塔机高度
   */
  initialCraneHeight: number;

  /**
   * 安拆单位名称
   */
  installationUnitName: string;

  /**
   * 安拆单位
   资质地址
   */
  installationUnitQualification: string;

  /**
   * 安全生产
   许可证地
   址
   */
  safetyProductionPermit: string;
  /**
   * 审核意见
   */
  auditMsg: string;
}

export interface SafeTaskForm extends BaseEntity {
  /**
   * 主键
   */
  openTaskId?: string | number;
  installations: Array<{
    installationId?: string | number;
    userName: string;
    userPositionName: string;
    face: string;
    certificate: string;
  }>;
  users: Array<{
    saleUserId?: string | number;
    mobile: string;
    userName: string;
    idCard: string;
    positionType: string;
  }>
  safeSupervisions: Array<{
    supervisionId?: string | number;
    userName: string;
    userPositionName: string;
    face: string;
    certificate: string;
  }>,
  /**
   * 项目id
   */
  projectId?: string | number;
  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 项目地址
   */
  projectAddress?: string;

  /**
   * 项目经度
   */
  projectLongitude?: string;

  /**
   * 项目维度
   */
  projectLatitude?: string;

  /**
   * 塔机现场编号
   */
  projectCraneNum?: string;

  /**
   * 塔机类型
   平臂:FlatArm
   动臂:SwingArm
   塔头:Tower
   缺少该字段默认传：
   FlatArm
   */
  craneType?: string;

  /**
   * 塔机规格型号
   */
  craneModel?: string;

  /**
   * 塔机出厂编号
   */
  craneSn?: string;

  /**
   * 塔机出厂日期
   */
  craneProductionDate?: string;

  /**
   * 产权单位名称
   */
  propertyCompanyName?: string;

  /**
   * 塔机生产厂商名称
   */
  factoryName?: string;

  /**
   * 顶升降节作业类型
   顶升:JackingUp
   降节:Disassembly
   */
  jackingType?: string;

  /**
   * 执行日期
   */
  executionDate?: string;

  /**
   * 升/降节数
   */
  sectionNum?: number;

  /**
   * 加降节后
   塔机高度
   */
  modifiedCraneHeight?: number;

  /**
   * 加降节前
   塔机高度
   */
  initialCraneHeight?: number;

  /**
   * 安拆单位名称
   */
  installationUnitName?: string;

  /**
   * 安拆单位
   资质地址
   */
  installationUnitQualification?: string;

  /**
   * 安全生产
   许可证地
   址
   */
  safetyProductionPermit?: string;
}

export interface SafeTaskQuery extends PageQuery {

  /**
   * 项目id
   */
  projectId?: string | number;
  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 塔机现场编号
   */
  projectCraneNum?: string;
  /**
   * 开始日期
   */
  executioinDateStart?: string;

  /**
   * 结束日期
   */
  executioinDateEnd?: string;

  /**
   * 状态
   */
  status?: string;
  /**
   * 日期范围参数
   */
  params?: any;
}
// 添加项目人员
export interface ProjectUserForm extends BaseEntity {
  saleUserId?: string | number;
  mobile: string;
  userName: string;
  idCard: string;
  positionType: string;
}
// 添加安拆人员
export interface ProjectInstallationForm extends BaseEntity {
  installationId?: string | number;
  userName: string;
  userPositionName: string;
  face: string;
  certificate: string;
}
// 添加旁站人员
export interface ProjectSupervisionForm extends BaseEntity {
  supervisionId?: string | number;
  userName: string;
  userPositionName: string;
  face: string;
  certificate: string;
}
