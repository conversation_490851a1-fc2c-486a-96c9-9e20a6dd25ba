<template>
  <div class="login">
    <div class="Softwaretitle">工程建设领域全生命周期安全隐患管控系统</div>

    <div class="login-container">
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
        <div class="login-title">账号登录</div>

        <el-form-item prop="username">
          <el-input v-model.trim="loginForm.username" type="text" size="large" auto-complete="off" :placeholder="proxy.$t('login.username')">
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model.trim="loginForm.password"
            :type="isShow ? 'text' : 'password'"
            size="large"
            auto-complete="off"
            :placeholder="proxy.$t('login.password')"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            <template #suffix>
              <el-icon v-if="!isShow" style="cursor: pointer" @click="handleShowPwd">
                <View />
              </el-icon>
              <el-icon v-else style="cursor: pointer" @click="handleShowPwd">
                <Hide />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="captchaEnabled" prop="code">
          <el-input
            v-model.trim="loginForm.code"
            size="large"
            auto-complete="off"
            :placeholder="proxy.$t('login.code')"
            style="width: 60%"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" class="login-code-img" @click="getCode" />
          </div>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button
            :loading="loading"
            size="large"
            style="
              width: 100%;
              background-color: #138bf6;
              border-radius: 6px;
              font-weight: bold;
              margin-top: 50px;
              font-size: 18px;
              height: 64px;
              color: #fff;
            "
            @click.prevent="handleLogin"
          >
            <span v-if="!loading">{{ proxy.$t('login.login') }}</span>
            <span v-else>{{ proxy.$t('login.logging') }}</span>
          </el-button>
        </el-form-item>
        <div @click="isregister = true" style="float: right; width: 100%; text-align: center; cursor: pointer; color: #138bf6">立即注册</div>
      </el-form>
    </div>

    <!--  底部  -->
    <div class="el-login-footer">
      主办单位：<a href="https://zjt.gansu.gov.cn/" target="_blank" style="cursor: pointer">甘肃省住房和城乡建设厅</a> &emsp; 技术支持单位：<span
        @click="gocompany"
        style="cursor: pointer"
        >甘肃建投数字科技有限公司</span
      ><br />
      <!-- <span>Copyright © 2023-{{ new Date().getFullYear() }} www.gsjtsz.cn All Rights Reserved</span> -->
    </div>

    <!-- 注册弹窗 -->
    <el-dialog v-model="isregister" title="用户注册" width="1200px">
      <div style="margin-bottom: 60px">
        <Register @closedialog="isregister = false" />
      </div>
      <template #footer>
        <span class="dialog-footer"> </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getCodeImg, getTenantList } from '@/api/login';
import { authBinding } from '@/api/system/social/auth';
import { useUserStore } from '@/store/modules/user';
import { LoginData, TenantVO } from '@/api/types';
import { to } from 'await-to-js';
import { HttpStatus } from '@/enums/RespEnum';
import { useI18n } from 'vue-i18n';
import Register from '@/components/Register/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const title = import.meta.env.VITE_APP_TITLE;
const userStore = useUserStore();
const router = useRouter();
const { t } = useI18n();
const isregister = ref(false);
const loginForm = ref<LoginData>({
  tenantId: '000000',
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: ''
} as LoginData);

const gocompany = () => {
  window.open('https://www.gsjtsz.com/');
};
const loginRules: ElFormRules = {
  tenantId: [{ required: true, trigger: 'blur', message: t('login.rule.tenantId.required') }],
  username: [{ required: true, trigger: 'blur', message: t('login.rule.username.required') }],
  password: [{ required: true, trigger: 'blur', message: t('login.rule.password.required') }],
  code: [{ required: true, trigger: 'change', message: t('login.rule.code.required') }]
};

const codeUrl = ref('');
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 租户开关
const tenantEnabled = ref(true);

// 注册开关
const register = ref(true);
const redirect = ref('/');
const loginRef = ref<ElFormInstance>();
// 租户列表
const tenantList = ref<TenantVO[]>([]);

watch(
  () => router.currentRoute.value,
  (newRoute: any) => {
    redirect.value = newRoute.query && newRoute.query.redirect && decodeURIComponent(newRoute.query.redirect);
  },
  { immediate: true }
);

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 localStorage 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        localStorage.setItem('tenantId', String(loginForm.value.tenantId));
        localStorage.setItem('username', String(loginForm.value.username));
        localStorage.setItem('password', String(loginForm.value.password));
        localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
      } else {
        // 否则移除
        localStorage.removeItem('tenantId');
        localStorage.removeItem('username');
        localStorage.removeItem('password');
        localStorage.removeItem('rememberMe');
      }
      // 调用action的登录方法
      const [err] = await to(userStore.login(loginForm.value));
      if (!err) {
        // const redirectUrl = redirect.value || '/';
        const redirectUrl = '/prj/prj_projects';
        await router.push(redirectUrl);
        loading.value = false;
      } else {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          await getCode();
        }
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  }
};

const getLoginData = () => {
  const tenantId = localStorage.getItem('tenantId');
  const username = localStorage.getItem('username');
  const password = localStorage.getItem('password');
  const rememberMe = localStorage.getItem('rememberMe');
  loginForm.value = {
    tenantId: tenantId === null ? String(loginForm.value.tenantId) : tenantId,
    username: username === null ? String(loginForm.value.username) : username,
    password: password === null ? String(loginForm.value.password) : String(password),
    rememberMe: rememberMe === null ? false : Boolean(rememberMe)
  } as LoginData;
};

/**
 * 获取租户列表
 */
const initTenantList = async () => {
  const { data } = await getTenantList(false);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
    if (tenantList.value != null && tenantList.value.length !== 0) {
      loginForm.value.tenantId = tenantList.value[0].tenantId;
    }
  }
};

/**
 * 第三方登录
 * @param type
 */
const doSocialLogin = (type: string) => {
  authBinding(type, loginForm.value.tenantId).then((res: any) => {
    if (res.code === HttpStatus.SUCCESS) {
      // 获取授权地址跳转
      window.location.href = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
let isShow = ref(false);
const handleShowPwd = () => {
  isShow.value = !isShow.value;
};
onMounted(() => {
  getCode();
  initTenantList();
  getLoginData();
});
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: url('../assets/images/bg.jpg') no-repeat;
  background-position: center;
  background-size: cover;
  padding-right: 160px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.Softwaretitle {
  position: absolute;
  top: 45px;
  left: 60px;
  font-family: Source Han Sans CN;
  font-weight: 600;
  font-size: 44px;
  color: #1a1a1a;
}

.login-container {
  min-height: 520px;
  width: 480px;
  background: #ffffff;
  border-radius: 12px;
  padding: 60px 40px;
  box-sizing: border-box;
}
.title-box {
  display: flex;

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }

  :deep(.lang-select--style) {
    line-height: 0;
    color: #7483a3;
  }
}

.login-form {
  width: 100%;
  .login-title {
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 22px;
    color: #1a1a1a;
    margin-bottom: 50px;
    width: 100%;
    text-align: center;
  }
  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;
  padding-left: 12px;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  width: 100%;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  left: 0;
  text-align: center;
  color: #313131;
  font-family: Arial, serif;
  font-size: 14px;
  letter-spacing: 1px;
}

.login-code {
  height: 58px;
  width: 40%;
}

.login-code-img {
  border-radius: 3px;
  border: 0.12rem solid #dcdfe6;
  width: 100%;
  height: 100%;
}

::v-deep .el-dialog__body {
  padding-bottom: 0px !important;
}

.el-form-item {
  margin-bottom: 30px !important;

  .el-input {
    height: 58px !important;
    border-radius: 6px !important;
  }
}
</style>
