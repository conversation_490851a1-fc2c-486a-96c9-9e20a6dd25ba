import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SafeTaskVO, SafeTaskForm, SafeTaskQuery } from '@/api/projects/safe/types';

/**
 * 查询【项目管理】安拆任务列表
 * @param query
 * @returns {*}
 */
// 嵌入的安拆任务列表数据接口
export const listSafeTask = (query?: SafeTaskQuery): AxiosPromise<SafeTaskVO[]> => {
  return request({
    url: '/projects/safeTask/list',
    method: 'get',
    params: query
  });
};

// 外面一层的安拆任务列表数据接口
export const listSafeTaskOut = (query?: SafeTaskQuery): AxiosPromise<SafeTaskVO[]> => {
  return request({
    url: '/projects/safeTask/listSupervision',
    method: 'get',
    params: query
  });
};
/**
 * 查询【项目管理】安拆任务详细
 * @param openTaskId
 */
export const getSafeTask = (openTaskId: string | number): AxiosPromise<SafeTaskVO> => {
  return request({
    url: '/projects/safeTask/taskDetail/' + openTaskId,
    method: 'get'
  });
};

/**
 * 新增【项目管理】安拆任务
 * @param data
 */
export const addSafeTask = (data: SafeTaskForm) => {
  return request({
    url: '/projects/safeTask/addPrj',
    method: 'post',
    data: data
  });
};

/**
 * 修改【项目管理】安拆任务
 * @param data
 */
export const updateSafeTask = (data: SafeTaskForm) => {
  return request({
    url: '/projects/safeTask/updPrj',
    method: 'post',
    data: data
  });
};

/**
 * 删除【项目管理】安拆任务
 * @param openTaskId
 */
export const delSafeTask = (openTaskId: string | number | Array<string | number>) => {
  return request({
    url: '/projects/safeTask/taskRemove/' + openTaskId,
    method: 'delete'
  });
};
// 根据项目id查询项目的相关信息
export const getProjectInfo = (projectId: string | number): AxiosPromise<any> => {
  return request({
    url: '/projects/safeTask/getToPrj/' + projectId,
    method: 'get'
  });
};
//获取所有项目列表
export const get_prj_search_data = (): AxiosPromise<any> => {
  return request({
    url: '/projects/prj_projects/allForSearchSupervision',
    method: 'get'
  });
}
// 上报安拆任务
export const reportSafeTask = (openTaskId: string | number): AxiosPromise<any> => {
  return request({
    url: '/projects/safeTask/upTask/' + openTaskId,
    method: 'post'
  });
}
// 审核安拆任务
export const auditSafeTask = (data: { openTaskId: string | number; status: number; auditMsg: string }): AxiosPromise<any> => {
  return request({
    url: '/projects/safeTask/auditTask',
    method: 'post',
    data
  });
}