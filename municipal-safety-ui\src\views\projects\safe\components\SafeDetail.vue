<template>
  <div class="SafeDetail">
    <!-- 项目详情弹窗 -->
    <el-dialog :title="detailDialog.title" v-model="detailDialog.visible" width="90vw"
      style="height: 90vh; overflow-y: auto" append-to-body @close="handleClose">
      <el-row class="safeDetailRow" v-if="isSafeDetailShow" v-loading="loading">
        <el-col :span="24">
          <el-card style="margin-bottom: 10px">
            <div class="basicinfo">
              <div class="basicinfo-left">
                <div class="basicinfotitle">
                  <div class="gang"></div>
                  <div class="basicinfoname">基本信息</div>
                </div>
                <el-descriptions label-width="140px" :column="2" border>
                  <el-descriptions-item label="项目名称">{{ detailForm.projectName }}</el-descriptions-item>
                  <el-descriptions-item label="项目地址">{{ detailForm.projectAddress }}</el-descriptions-item>
                  <el-descriptions-item label="项目位置地图">
                    <div>经度：{{ detailForm.projectLongitude }}；纬度：{{ detailForm.projectLatitude }}</div>
                    <el-button v-if="detailForm.projectLongitude" type="primary" link
                      @click="viewMap(detailForm.projectLongitude + ',' + detailForm.projectLatitude)">查看</el-button>
                  </el-descriptions-item>
                  <el-descriptions-item label="塔机现场编号">{{ detailForm.projectCraneNum }}</el-descriptions-item>
                  <el-descriptions-item label="塔机类型">
                    <dict-tag :options="safe_crane_type" :value="detailForm.craneType" />
                  </el-descriptions-item>
                  <el-descriptions-item label="塔机规格型号">{{ detailForm.craneModel }}</el-descriptions-item>
                  <el-descriptions-item label="塔机出厂编号">{{ detailForm.craneSn }}</el-descriptions-item>
                  <el-descriptions-item label="塔机出厂日期">{{ parseTime(detailForm.craneProductionDate, '{y}-{m}-{d}')
                  }}</el-descriptions-item>
                  <el-descriptions-item label="产权单位名称">{{ detailForm.propertyCompanyName }}</el-descriptions-item>
                  <el-descriptions-item label="塔机生产厂商名称">{{ detailForm.factoryName }}</el-descriptions-item>
                  <el-descriptions-item label="顶升降节作业类型">
                    <dict-tag :options="safe_jacking_type" :value="detailForm.jackingType" />
                  </el-descriptions-item>
                  <el-descriptions-item label="执行日期">{{ parseTime(detailForm.executionDate, '{y}-{m}-{d}')
                  }}</el-descriptions-item>
                  <el-descriptions-item label="升/降节数">{{ detailForm.sectionNum }}</el-descriptions-item>
                  <el-descriptions-item label="加降节前塔机高度">{{ detailForm.initialCraneHeight }}</el-descriptions-item>
                  <el-descriptions-item label="加降节后塔机高度">{{ detailForm.modifiedCraneHeight }}</el-descriptions-item>
                  <el-descriptions-item label="安拆单位名称">{{ detailForm.installationUnitName }}</el-descriptions-item>
                  <el-descriptions-item label="安拆单位资质地址">{{ detailForm.installationUnitQualification
                  }}</el-descriptions-item>
                  <el-descriptions-item label="安全生产许可证地址" label-width="150px">{{ detailForm.safetyProductionPermit
                  }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-card>
          <!-- 项目人员信息 -->
          <el-card style="margin-bottom: 10px">
            <div slot="header">
              <div class="punits">
                <div class="basicinfotitle">
                  <div class="gang"></div>
                  <div class="basicinfoname">项目人员</div>
                </div>
              </div>
            </div>
            <!-- 项目人员信息 -->
            <el-table stripe :data="detailForm.users" border style="width: 100%">
              <el-table-column prop="userName" label="姓名" />
              <el-table-column prop="idCard" label="身份证号" />
              <el-table-column prop="mobile" label="手机号" />
              <el-table-column prop="positionType" label="岗位类型">
                <template #default="scope">
                  <dict-tag :options="safe_position_type" :value="scope.row.positionType" />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
          <!-- 安拆人员 -->
          <el-card style="margin-bottom: 10px">
            <div slot="header">
              <div class="punits">
                <div class="basicinfotitle">
                  <div class="gang"></div>
                  <div class="basicinfoname">安拆人员</div>
                </div>
              </div>
            </div>
            <!-- 安拆人员信息 -->
            <el-table stripe :data="detailForm.installations" border style="width: 100%">
              <el-table-column prop="userName" label="姓名" />
              <el-table-column prop="userPositionName" label="岗位名称" />
              <el-table-column prop="face" label="人脸照片" />
              <el-table-column prop="certificate" label="资质证书" />
            </el-table>
          </el-card>
          <!-- 旁站人员 -->
          <el-card style="margin-bottom: 10px">
            <div slot="header">
              <div class="punits">
                <div class="basicinfotitle">
                  <div class="gang"></div>
                  <div class="basicinfoname">旁站人员</div>
                </div>
              </div>
            </div>
            <!-- 旁站人员信息 -->
            <el-table stripe :data="detailForm.safeSupervisions" border style="width: 100%">
              <el-table-column prop="userName" label="姓名" />
              <el-table-column prop="userPositionName" label="岗位名称" />
              <el-table-column prop="face" label="人脸照片" />
              <el-table-column prop="certificate" label="资质证书" />
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getSafeTask } from '@/api/projects/safe/index'
import { SafeTaskForm } from '@/api/projects/safe/types'

const props = defineProps({
  openTaskId: {
    type: [String, Number],
    default: undefined
  }
});

const emit = defineEmits(['emitSafeDetail']);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { safe_crane_type, safe_jacking_type, safe_position_type } = toRefs<any>(proxy?.useDict('safe_crane_type', 'safe_jacking_type', 'safe_position_type'));

const detailDialog = reactive<DialogOption>({
  visible: false,
  title: '',
});
const detailForm = reactive<SafeTaskForm>({} as SafeTaskForm);
const loading = ref(true)
const isSafeDetailShow = ref(false)

watch(() => props.openTaskId, (newVal) => {
  if (newVal) {
    handleViewDetail(newVal);
  }
})

/** 查看地图 */
const viewMap = (lola: string) => {
  if (lola) {
    window.open(`https://uri.amap.com/marker?position=${lola}`, '_blank');
  }
};
/** 查看项目详情的事件方法 */
const handleViewDetail = async (openTaskId: string | number) => {
  detailDialog.visible = true;
  detailDialog.title = '安拆任务详情';
  isSafeDetailShow.value = true
  try {
    const res = await getSafeTask(openTaskId);
    Object.assign(detailForm, res.data);
    detailDialog.visible = true;
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取项目详情失败', error);
    proxy?.$modal.msgError('获取项目详情失败');
  } finally {
    loading.value = false
  }
};

/** 关闭详情弹窗 */
const handleClose = () => {
  loading.value = true
  isSafeDetailShow.value = false
  emit('emitSafeDetail');
}
</script>

<style lang="scss" scoped>
.prodetail {
  border-bottom: 1px solid rgba(64, 158, 255, 0);
}

.prodetail:hover {
  color: #409eff;
  border-bottom: 1px solid rgb(64, 158, 255);
}

.basicinfo {
  display: flex;
  align-items: center;

  .ercode {
    text-align: center;
    margin-right: 40px;
  }

  .basicinfo-left {
    flex: 1;
  }
}

.basicinfotitle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .gang {
    height: 15px;
    width: 3px;
    background-color: #409eff;
    border-radius: 6px;
    margin-right: 5px;
  }

  .basicinfoname {
    font-weight: 600;
  }
}
</style>
<style lang="scss">
.safeDetailRow {
  .el-loading-mask {
    .el-loading-spinner {
      top: 35% !important;
    }
  }
}
</style>