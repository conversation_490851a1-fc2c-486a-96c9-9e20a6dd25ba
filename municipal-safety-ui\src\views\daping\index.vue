<template>
  <div class="dashboard-container">
    <!-- 顶部标题栏 -->
    <!-- <BorderBox8 style="height: 80px">
      <div class="dashboard-header">
        <div class="time-display">
          {{ currentweather.city }} {{ currentweather.qu }} {{ currentweather.dayweather }}
          {{ `温度：${currentweather.nighttemp}~${currentweather.daytemp}℃` }}
        </div>
        <div class="header-info">
          <div class="time-display">
            <div style="display: flex; align-items: center">
              <Decoration6 style="width: 300px; height: 50px"></Decoration6><span style="font-size: 20px; font-weight: bold"> 数据监控中 </span
              ><Decoration6 style="width: 300px; height: 50px"></Decoration6>
            </div>
          </div>
        </div>
        <div class="header-info">
          <div class="time-display">{{ currentTime }}</div>
        </div>
      </div>
    </BorderBox8> -->
    <div class="headbox">
      <div class="time-display headboxsub" style="text-align: left" v-if="showWeather">
        {{ currentweather.province }} {{ currentweather.city }} {{ currentweather.weather }}
        {{ currentweather.temperature_float ? `当前温度：${currentweather.temperature_float} ℃` : '当前温度：暂无数据' }}
        <br />
        {{ currentweather.windpower ? `风力：${currentweather.windpower}级` : '风力：暂无数据' }}
        {{ currentweather.winddirection ? `风向：${currentweather.winddirection}` : '风向：暂无数据' }}
        <br />
        {{ currentweather.humidity_float ? `湿度：${currentweather.humidity_float}%` : '湿度：暂无数据' }}
      </div>
      <div>
        <div class="time-display" style="display: flex; align-items: center; width: 100%">
          <!-- <Decoration6 style="height: 40px"></Decoration6> -->
          <div class="pjname">
            <div></div>
            <!-- <div>中华人民共和国住房和城乡建设部</div> -->
            <!-- {{ user.level == 'country' ? '全省' : user.level == 'province' ? '全市' : user.level == 'city' ? '全县' : '全区' }} -->
            <!-- {{ currentplace.name
            }}{{
              currentplace.level == 'country'
                ? '住房和城乡建设部'
                : currentplace.level == 'province'
                  ? '住房和城乡建设厅'
                  : currentplace.level == 'city'
                    ? '住房和城乡建设局'
                    : '质监站'
            }}
            <div>危险性较大的分部分项工程安全信息预警大屏</div> -->
            <div>{{ currentplace.name == '中华人民共和国' ? '' : currentplace.name }}工程建设领域全生命周期安全隐患态势感知平台</div>
            <!-- <div>
              <div style="height: 30px"></div>
              {{
                currentplace.level == 'country' ? '全国' : currentplace.level == 'province' ? '全省' : currentplace.level == 'city' ? '全市' : '全区'
              }}危险性较大的分部分项工程安全信息预警大屏
            </div> -->
          </div>
          <!-- <Decoration6 style="height: 40px"></Decoration6> -->
        </div>
      </div>
      <div class="time-display headboxsub">{{ currenttime }}</div>
    </div>

    <!-- 主内容区 -->
    <div class="dashboard-content">
      <!-- 左侧数据面板 -->
      <div class="panel left-panel">
        <div class="data-card data-cardmo1">
          <div class="typebox">
            <div
              v-for="item in menunav"
              :class="{ 'typebox_item': true, 'activetpy': activevalue == item.itemsType }"
              @click="changetype(item.itemsType, item.itemsNum)"
            >
              {{ item.itemsName }} <span>{{ item.itemsNum }}</span>
            </div>
            <!-- <div :class="{ 'typebox_item': true, 'activetpy': activevalue == 2 }" @click="changetype(2)">超危大<span>852</span></div> -->
          </div>
          <!-- <div style="margin: 10px 0px; height: 42px">
            <el-date-picker
              style="height: 100%"
              v-model="value1"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :size="size"
            />
          </div> -->
          <div class="typeboxlist">
            <div
              @click="changelistvalue(item.itemsName, item.itemsNum)"
              :class="{ 'data-row': true, isactlisttype: listvalue == item.itemsName }"
              v-for="item in typelist"
            >
              <span>{{ item.itemsName }}</span>
              <div style="height: 100%; display: flex; align-items: center">
                <div>涉及工程数量：{{ item.itemsNum }}</div>
                <div class="chulildata">
                  <p style="margin: 0; color: #67c23a" class="value">
                    已处理<span style="margin: 0px 2px">{{ item.yfjNum }}</span>
                  </p>
                  <p style="margin: 0; color: #e6a23c" class="value">
                    未处理<span style="margin: 0px 2px">{{ item.dfjNum }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <BorderBox8 style="width: 100%; height: 40%" id="box">
          <div class="data-card datalist">
            <div class="scroll-containeryuj" style="margin-left: 1px">
              {{
                currentplace.level == 'country' ? '全国' : currentplace.level == 'province' ? '全省' : currentplace.level == 'city' ? '全市' : '全区'
              }}危险性较大的分部分项工程年度数量统计
            </div>
            <div class="echartsleft" id="echartsleft"></div>
          </div>
        </BorderBox8>
      </div>
      <!-- 中间地图区域 -->
      <div class="map-area">
        <BorderBox1 class="container BorderBox1box">
          <ChinaMap
            :maplat="projectdata"
            :map-data="provinceData"
            :typedata="activevalue"
            :listpyetdata="listvalue"
            @getcode="getcode"
            :selected-province="selectedProvince"
            @province-click="handleProvinceClick"
          />
        </BorderBox1>
      </div>

      <!-- 右侧数据面板 -->
      <div class="panel right-panel">
        <div class="datacar">
          <div class="scroll-containeryuj" style="margin-bottom: 0px">
            {{
              currentplace.level == 'country' ? '全国' : currentplace.level == 'province' ? '全省' : currentplace.level == 'city' ? '全市' : '全区'
            }}预警工程项目 TOP101
          </div>
          <div class="projectbox">
            <div
              @click="checkpj(item)"
              :class="{ 'projectitem': true, 'dbg': index % 2 != 0, 'currentpjbg': currentpj == item.project_id }"
              v-for="(item, index) in projectdata"
            >
              <span :class="{ 'num': true }"><span v-if="index != 9">0</span>{{ Number(index) + 1 }}</span>
              <div>{{ item.project_name }}</div>
            </div>
          </div>
        </div>
        <div class="data-card datalistt">
          <div class="scroll-containeryuj" style="margin-left: 1px">危险性较大的分部分项工程地区数量统计</div>
          <div class="echartsleft" id="echartsright"></div>
        </div>
      </div>
    </div>

    <!-- 底部信息栏 -->
    <!-- <div class="dashboard-footer">
      <p>数据来源：国家统计局 | 技术支持：甘肃建投数字科技有限公司</p>
    </div> -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import ChinaMap from './Dashboard.vue';
import { BorderBox1 } from '@dataview/datav-vue3/es/components/border-box-1';
import { BorderBox8 } from '@dataview/datav-vue3/es/components/border-box-8';
import * as echarts from 'echarts';
import { getlist, getItemNum, getDangerNum, getProItemLis, getYearItemNum, getAreaItemNum } from '@/api/daping/index.js';
import { ElMessageBox } from 'element-plus';
let timer = ref(null);
let menunav = ref([
  { itemsName: '危大', itemsType: '1', itemsNum: 1 },
  { itemsName: '超危大', itemsType: '2', itemsNum: 1 }
]);
let maplat = ref([]);
let activevalue = ref(1);
let chartDomright = null;
let leftecharts = null;
let showWeather = ref(true);
let rightecharts = null;
let currentplace = ref({
  name: ''
});
// let user = ref({
//   adcode: '100000',
//   level: 'country',
//   name: '全国'
// });
let user = ref({
  adcode: '620000',
  level: 'province',
  name: '甘肃省'
});
// let user = ref({
//   adcode: 620700,
//   level: 'city',
//   name: '张掖市'
// });
let listvalue = ref(1);
let currenttime = ref('');
//列表数据初始化
const size = (ref < 'default') | 'large' | ('small' > 'default');
const typelist = ref([
  {
    name: '基坑工程',
    id: 1,
    value: 35
  },
  {
    name: '模板工程及支撑体系',
    id: 2,

    value: 43
  },
  {
    name: '起重吊装及起重机械安装拆卸工程',
    id: 3,

    value: 53
  },
  {
    name: '脚手架工程',
    id: 4,

    value: 14
  },
  {
    name: '拆除工程',
    id: 5,

    value: 34
  },
  {
    name: '暗挖工程',
    id: 6,

    value: 55
  },
  {
    name: '其他',
    id: 7,

    value: 66
  }
]);

const value1 = ref('');
const currentpj = ref();
// 模拟数据
let projectdata = ref([
  { project_name: '工程项目名称工程项目名称工程项目名称1', value: 12.91, rank: 1, growth: 8.0, id: 1 },
  { project_name: '工程项目名称工程项目名称工程项目名称2', value: 11.64, rank: 2, growth: 8.6, id: 2 },
  { project_name: '工程项目名称工程项目名称工程项目名称3', value: 8.31, rank: 3, growth: 8.3, id: 3 },
  { project_name: '工程项目名称工程项目名称工程项目名称4', value: 7.35, rank: 4, growth: 8.5, id: 4 },
  { project_name: '工程项目名称工程项目名称工程项目名称5', value: 5.89, rank: 5, growth: 7.1, id: 5 },
  { project_name: '工程项目名称工程项目名称工程项目名称6', value: 5.39, rank: 6, growth: 8.2, id: 6 },
  { project_name: '工程项目名称工程项目名称工程项目名称7', value: 5.0, rank: 7, growth: 12.9, id: 7 },
  { project_name: '工程项目名称工程项目名称工程项目名称8', value: 4.88, rank: 8, growth: 8.0, id: 8 },
  { project_name: '工程项目名称工程项目名称工程项目名称9', value: 4.6, rank: 9, growth: 7.7, id: 9 },
  { project_name: '工程项目名称工程项目名称工程项目名称0', value: 4.3, rank: 10, growth: 8.3, id: 10 }
]);
const currentweather = ref({});

const checkcity = (item) => {
  // console.log('点击左侧省份item', item);
};
// 获取左侧统计表数据
const updataleftcharts = async (obj) => {
  console.log('2333333333', obj);
  const res = await getYearItemNum(obj);
  console.log('获取左侧表格数据', res);
  if (res.code == 200) {
    const newOption = {
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: res.data.time,
          axisLabel: {
            textStyle: {
              color: '#fff',
              fontSize: 14
            }
          }
        }
      ], // 更新 x 轴

      series: [
        { data: res.data.weidadata }, // 更新第一个系列（危大）
        { data: res.data.chaodata } // 更新第二个系列（超危大）
      ]
    };
    leftecharts.setOption(newOption, { notMerge: false });
  }
};
// 获取右侧统计表数据
const updatarightcharts = async (obj) => {
  const res = await getAreaItemNum(obj);
  if (res.code == 200) {
    const newOption = {
      xAxis: [
        {
          axisLabel: {
            textStyle: {
              color: '#fff',
              fontSize: 12
            }
          },
          type: 'category',
          data: res.data.address, //地区
          axisLabel: {
            interval: 0, // 强制显示所有标签
            rotate: 45, // 标签旋转 45 度（防重叠）
            width: 80, // 限制标签宽度
            overflow: 'break' // 超出换行
          }
        }
      ],
      dataZoom: [
        // 只有数据超过7条时才启用dataZoom
        {
          type: 'inside',
          xAxisIndex: 0,
          zoomOnMouseWheel: true,
          moveOnMouseMove: true,
          moveOnMouseWheel: true,
          zoomLock: false, // 允许缩放
          filterMode: 'filter', // 或 'empty' 根据需求
          zoomSensitivity: 0.1, // 默认1，值越小越灵敏
          start: 0, // 数据展示窗口的初始数据
          end: res.data.address.length > 10 ? 30 : 100
        },
        {
          type: 'slider',
          xAxisIndex: 0,
          bottom: 10
        }
      ],
      series: [
        {
          data: [...res.data.weidadata]
        },
        {
          data: [...res.data.chaodata]
        }
      ]
    };
    rightecharts.setOption(newOption, { notMerge: false });
  }
};
// 获取危大项目
const getproitemlistop = async (obj) => {
  const res = await getProItemLis(obj).catch((e) => {
    projectdata.value = [];
  });
  if (res.code == 200) {
    res.data.map((e) => {
      e.lola = e.lola.split(',');
      e.lola = e.lola.map((r) => (r = Number(r)));
    });
    projectdata.value = res.data;
    console.log('获取工程项目列表', projectdata.value);
  } else {
    projectdata.value = [];
  }
};
// 获取危大项目清单类型
const getwdlist = async (obj) => {
  const res = await getItemNum(obj);
  console.log('危大清单类型res', res);
  if (res.code == 200) {
    menunav.value = res.data;
  }
};
// 获取危大项目清单
const getdangernumlist = async (obj) => {
  const res = await getDangerNum(obj);
  console.log('危大清单列表res', res);
  if (res.code == 200) {
    typelist.value = res.data;
  }
};
const enterFullscreen = () => {
  const isFullscreen =
    document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;

  if (!isFullscreen) {
    document.documentElement.requestFullscreen();
  } else {
    document.exitFullscreen();
  }
};

const checkpj = (item) => {
  // currentpj.value = item.id;
  window.open(item.url);
  // if (import.meta.env.DEV) {
  //   // 开发
  //   window.open('http://localhost:' + import.meta.env.VITE_APP_PORT + item.url);
  // } else {
  //   // 生产
  //   window.open(import.meta.env.VITE_APP_BASE_API + item.url);
  // }
};
// 当前选中的省份
const selectedProvince = ref('');

// 处理省份点击
const handleProvinceClick = (provinceName) => {
  selectedProvince.value = provinceName;
};

// 当前省份数据
const currentProvince = computed(() => {
  return provinceData.value.find((item) => item.name === selectedProvince.value) || {};
});

const dateFormat = () => {
  const now = new Date();
  let date = new Date(now);
  let year = date.getFullYear();
  let wk = date.getDay();
  /* 在日期格式中，月份是从0开始的，因此要加0
   * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
   * */
  let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
  let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  let hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
  let minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  let seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  let weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  let week = weeks[wk];
  currenttime.value = year + '年' + month + '月' + day + '日' + ' ' + hours + ':' + minutes + ':' + seconds + ' ' + week;
  // 拼接
  // return year + '年' + month + '月' + day + '日' + ' ' + hours + ':' + minutes + ':' + seconds + ' ' + week;
};

const getweather = (cityCodenum) => {
  console.log('cityCodenum', cityCodenum);
  const apiKey = 'c9ddeb6754cf630c9fccce37f8597a24'; // 替换为你的Key
  var cityCode = cityCodenum; // 城市编码（如北京朝阳区：110105）
  if (cityCode == 710100) {
    cityCode = 710000;
  }
  // 获取天气预报（extensions=all表示预报天气）
  // console.log('cityCodenum', cityCodenum);
  fetch(`https://restapi.amap.com/v3/weather/weatherInfo?key=${apiKey}&city=${cityCode}&extensions=base`)
    .then((response) => response.json())
    .then((data) => {
      if (data.status === '1') {
        // console.log('天气预报数据：', data);
        currentweather.value = data.lives[0];
        // currentweather.value.city = forecast.province; //城市
        // currentweather.value.qu = forecast.city; //区
        // console.log('天气预报数据：', currentweather.value);
        // console.log(`城市：${forecast.province} ${forecast.city}`);
        // forecast.casts.forEach((day) => {
        //   console.log(`日期：${day.date}`, `白天天气：${day.dayweather}`, `温度：${day.nighttemp}~${day.daytemp}℃`);
        // });
      } else {
        console.error('请求失败：', data.info);
      }
    })
    .catch((error) => console.error('请求异常：', error));
};
/**
 * 将省级行政区adcode转换为该省下第一个市级行政区的adcode
 * @param {string} provinceAdcode - 省级行政区adcode（格式为XX0000）
 * @returns {string} 市级行政区adcode（格式为XXXX00）
 */
const convertProvinceToCityAdcode = (provinceAdcode) => {
  // 验证输入是否为有效的省级adcode格式
  if (!/^\d{2}0000$/.test(provinceAdcode)) {
    console.error('Invalid province adcode format');
    return provinceAdcode; // 如果不是省级格式，直接返回原值
  }

  // 省级adcode前两位是省市代码，后四位是0000
  // 市级adcode前四位是省市代码，后两位是00
  // 如果数字转换为字符串
  const provinceCode = provinceAdcode.toString().substring(0, 2);
  return provinceCode + '0100'; // 通常0100是该省的省会城市

  // 如果需要更精确的转换，可以维护一个省份到主要城市的映射表
  // 例如：
  // const provinceToCityMap = {
  //     '110000': '110100', // 北京
  //     '120000': '120100', // 天津
  //     '130000': '130100', // 河北省->石家庄
  //     // 其他省份映射...
  //     '220000': '220100'  // 吉林省->长春
  // };
  // return provinceToCityMap[provinceAdcode] || (provinceCode + '0100');
};

// 示例：地图点击事件处理函数

// 使用示例（假设你有一个地图点击事件）
// map.on('click', handleMapClick);
const updateData = (arr) => {
  // console.log('arr', arr);
  // 跟新数据
  // console.log('data', arr);
  // 更新图表
  // console.log('chartDomright', rightecharts);
  rightecharts.setOption({
    xAxis: [
      {
        axisLabel: {
          textStyle: {
            color: '#fff',
            fontSize: 12
          }
        },
        type: 'category',
        data: arr.map((e) => e.divisionName), //地区
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 45, // 标签旋转 45 度（防重叠）
          width: 80, // 限制标签宽度
          overflow: 'break' // 超出换行
        }
      }
    ],

    dataZoom: [
      // 只有数据超过7条时才启用dataZoom
      {
        type: 'inside',
        xAxisIndex: 0,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
        zoomLock: false, // 允许缩放
        filterMode: 'filter', // 或 'empty' 根据需求
        zoomSensitivity: 0.1, // 默认1，值越小越灵敏
        start: 0, // 数据展示窗口的初始数据
        end: arr.length > 10 ? 30 : 100
      },
      {
        type: 'slider',
        xAxisIndex: 0,
        bottom: 10
      }
    ],
    // grid: {
    //   bottom: arr.length > 7 ? '15%' : '10%' // 根据是否启用dataZoom调整底部间距
    // },
    series: [
      {
        name: '危大',
        type: 'bar',
        barWidth: '40%', // 控制柱宽
        data: [],
        itemStyle: {
          normal: {
            barBorderRadius: [4, 4, 0, 0],
            //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(80,141,255,0.8)'
              },
              {
                offset: 1,
                color: 'rgba(38,197,254,0.5)'
              }
            ])
          }
        }
      },
      {
        name: '超危大',
        type: 'bar',
        barWidth: '40%',
        data: [],
        itemStyle: {
          normal: {
            barBorderRadius: [4, 4, 0, 0],
            //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(29, 250, 255,0.8 )'
              },
              {
                offset: 1,
                color: 'rgba(29, 250, 255,0.5 )'
              }
            ])
          }
        }
      }
    ]
  });
};

const getcode = async (data) => {
  currentplace.value = data;
  // 获取危大类型
  await getwdlist({ areaCode: data.adcode == '100000' ? '' : data.adcode == '620129' ? '620122' : data.adcode });
  // 获取危大类型清单
  await getdangernumlist({ areaCode: data.adcode == '100000' ? '' : data.adcode == '620129' ? '620122' : data.adcode, itemsType: activevalue.value });
  // 默认选择第一个清单项目
  listvalue.value = typelist.value[0].itemsName;
  // 获取项目数据
  await getproitemlistop({
    areaCode: data.adcode == '100000' ? '' : data.adcode == '620129' ? '620122' : data.adcode,
    itemsType: activevalue.value,
    dangerName: listvalue.value
  });
  // 左侧统计表
  await updataleftcharts({ areaCode: data.adcode == '100000' ? '' : data.adcode == '620129' ? '620122' : data.adcode });
  // 右侧统计表
  await updatarightcharts({ areaCode: data.adcode == '100000' ? '' : data.adcode == '620129' ? '620122' : data.adcode });
  console.log(2);
  const res = await getlist({
    parentCode: data.adcode
  });

  if (data.adcode == 100000) {
    getweather(110100);
  } else {
    if (/^\d{2}0000$/.test(data.adcode)) {
      // 转换为市级行政区
      const cityAdcode = convertProvinceToCityAdcode(data.adcode);
      // console.log(`转换为市级行政区 ${data.adcode} to city ${cityAdcode}`);
      // 这里可以执行你需要的操作，比如加载该市级行政区的地图数据
      // loadCityData(cityAdcode);
      // return cityAdcode;
      getweather(cityAdcode);
    } else {
      if (data.adcode == 620129) {
      } else {
        getweather(data.adcode);
      }
    }
  }
};
const changetype = async (type, num) => {
  if (activevalue.value == type || num == 0) {
    return;
  }
  listvalue.value = null;
  activevalue.value = type;
  console.log(' activevalue.value', activevalue.value);
  await getdangernumlist({ areaCode: currentplace.value.adcode == '100000' ? '' : currentplace.value.adcode, itemsType: activevalue.value });
  listvalue.value = typelist.value[0].itemsName;
  // 获取项目数据
  await getproitemlistop({
    areaCode: currentplace.value.adcode == '100000' ? '' : currentplace.value.adcode,
    itemsType: activevalue.value,
    dangerName: listvalue.value
  });
};
const changelistvalue = async (type, num) => {
  if (num == 0) {
    ElMessageBox.alert('数据正在建设中...', '提示', {
      type: 'warning',
      confirmButtonText: 'OK'
    });
    return;
  }
  if (listvalue.value == type) {
    return;
  }
  listvalue.value = type;
  let obj = {
    areaCode: currentplace.value.adcode == '100000' ? '' : currentplace.value.adcode,
    itemsType: activevalue.value,
    dangerName: listvalue.value.itemsName
  };
  console.log(obj);
  await getproitemlistop(obj);
};
/**
 * @Description: 鼠标移动事件
 * @Author: admin
 */
const testMove = () => {
  clearTimeout(timer.value);
};
const getleftecharts = () => {
  var chartDom = document.getElementById('echartsleft');
  leftecharts = echarts.init(chartDom);
  var option;
  option = {
    title: { show: true, text: '单位：个', textStyle: { fontSize: 14, color: '#fff' } },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['危大', '超危大'],
      left: 'right',
      textStyle: {
        fontSize: 12, //字体大小
        color: '#ffffff', //字体颜色
        fontWeight: 400
      }
    },

    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
        axisLabel: {
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        }
      }
    ],

    yAxis: [
      {
        minInterval: 1,
        type: 'value',
        scale: true, // 开启自适应缩放
        axisLabel: {
          color: '#fff',
          fontSize: 14,
          formatter: '{value}' // 数值直接显示（不格式化）
        },
        // 动态计算 min 和 max（确保为整数）
        min: function (value) {
          if (value.min === Infinity || value.max === -Infinity) return 0; // 无数据时默认为 0
          const minVal = Math.floor(value.min);
          return minVal >= 0 && value.max - minVal <= 5 ? 0 : minVal - 1; // 留出间距
        },
        max: function (value) {
          if (value.min === Infinity || value.max === -Infinity) return 10; // 无数据时默认 0-10
          return Math.ceil(value.max) + 1; // 向上取整并留出间距
        },
        // 动态计算刻度间隔（保证为整数）

        splitLine: {
          show: true, // 显示刻度线
          lineStyle: { type: 'dashed', color: '#124977' }
        },

        color: '#fff',
        nameGap: 40, // y轴name与横纵坐标轴线的间距
        nameTextStyle: {
          color: '#fff',
          fontWeight: 400,
          fontSize: 14
        },
        position: 'top'
      }
    ],
    series: [
      {
        name: '危大',
        type: 'line',

        // areaStyle: {
        //   color: 'linear-gradient(0deg, rgba(25,119,228,0.1) 0%, #1977E4 100%)'
        // },
        areaStyle: {
          normal: {
            //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'RGBA(179, 114, 31, 0.5)'
              },
              {
                offset: 1,
                color: 'RGBA(179, 114, 31, 0)'
              }
            ])
          }
        },
        lineStyle: {
          color: 'RGBA(179, 114, 31, 1)'
        },
        emphasis: {
          focus: 'series'
        },
        data: [20, 20, 25, 23, 27, 24, 20]
      },
      {
        name: '超危大',
        type: 'line',
        symbol: 'circle', // 显示数据点

        areaStyle: {
          normal: {
            //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'RGBA(47, 136, 223, 0.5)'
              },
              {
                offset: 1,
                color: 'RGBA(47, 136, 223, 0)'
              }
            ])
          }
        },
        lineStyle: {
          color: 'RGBA(47, 136, 223, 1)'
        },
        emphasis: {
          focus: 'series'
        },
        data: [21, 24, 21, 18, 23, 20, 21]
      }
    ]
  };

  option && leftecharts.setOption(option);
};
const getrightecharts = () => {
  chartDomright = document.getElementById('echartsright');
  rightecharts = echarts.init(chartDomright);
  let option;

  option = {
    title: { show: true, text: '单位：个', textStyle: { fontSize: 14, color: '#fff' } },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      left: 'right',
      textStyle: {
        fontSize: 14, //字体大小
        color: '#ffffff', //字体颜色
        fontWeight: 400
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      axisLabel: {
        textStyle: {
          color: '#fff',
          fontSize: 14
        }
      },
      type: 'category',
      data: [] // 原来的系列名称现在作为x轴分类
    },
    yAxis: {
      minInterval: 1,
      axisLabel: {
        textStyle: {
          color: '#fff',
          fontSize: 14
        }
      },
      position: 'top',
      type: 'value',
      color: '#fff',
      nameGap: 40, // y轴name与横纵坐标轴线的间距
      nameTextStyle: {
        color: '#fff',
        fontWeight: 400,
        fontSize: 14
      },
      boundaryGap: [0, 0.01]
    },
    series: [
      {
        name: '危大',
        type: 'bar',
        data: [],
        itemStyle: {
          normal: {
            barBorderRadius: [4, 4, 0, 0],
            //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'RGBA(241, 149, 35, 0.8)'
              },
              {
                offset: 1,
                color: 'RGBA(241, 149, 35, 0.5)'
              }
            ])
          }
        }
      },
      {
        name: '超危大',
        type: 'bar',
        data: [],
        itemStyle: {
          normal: {
            barBorderRadius: [4, 4, 0, 0],
            //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'RGBA(51, 148, 242, 0.8)'
              },
              {
                offset: 1,
                color: 'RGBA(51, 148, 242, 0.5)'
              }
            ])
          }
        }
      }
    ]
  };

  option && rightecharts.setOption(option);
};
/**
 * @Description: 鼠标离开事件
 * @Author: admin
 */

onMounted(() => {
  dateFormat();
  getleftecharts();
  getrightecharts();
  timer.value = setInterval(() => {
    dateFormat(); // 修改数据date
  }, 1000);
  enterFullscreen();
});
onBeforeUnmount(() => {
  testMove();
});
</script>

<style scoped lang="scss">
* {
  box-sizing: border-box;
}
.dashboard-container {
  width: 100%;
  height: 100vh;
  /* background: linear-gradient(135deg, #0f2027, #203a43, #2c5364); */
  color: #fff;
  font-family: 'Microsoft YaHei', sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: radial-gradient(circle, rgba(0, 0, 0, 0) 0%, #00040e 100%);
  background-image: url('@/assets/daping/bj.png');
  background-repeat: no-repeat;
  background-size: cover;
}

.dashboard-header {
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
  // height: 80px;
}

.dashboard-header h1 {
  margin: 0;
  font-size: 24px;
  /* background: linear-gradient(to right, #fff, #a7c7ff); */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.time-display {
  font-size: 16px;
  color: #a7c7ff;
}

.data-update {
  font-size: 12px;
  color: #7f9eb2;
  margin-top: 5px;
}

.dashboard-content {
  display: flex;
  height: calc(100% - 90px);
  padding: 0px 30px;
  align-items: center;
}

.panel {
  display: flex;
  flex-direction: column;
  background-color: transparent;
  border: none;
}
.left-panel {
  width: 25%;
  margin-right: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.right-panel {
  width: 25%;
  height: 100%;
  margin: 0px;
  padding: 0px;
  margin-left: 20px;
}
.left-panel .dv-border-box-8 {
  overflow: hidden;
}
.map-area {
  /* background: rgba(16, 31, 63, 0.5); */
  border-radius: 10px;
  /* position: relative; */
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90%;
}
.BorderBox1box {
  max-width: 100%;
}
.data-card {
  box-sizing: border-box;
}
.data-cardmo1 {
  height: 55%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.datacar {
  padding: 0px;
  height: calc(70% - 20px);
  margin-bottom: 20px;
}
.data-card h3 {
  margin-top: 0;
  /* margin-bottom: 20px; */
  color: #a7c7ff;
  border-bottom: 1px solid #2a3f6f;
  /* padding-bottom: 10px; */
}

.rank-item {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  margin-bottom: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.rank-item:hover {
  background: rgba(26, 42, 108, 0.5);
}

.rank-item.active {
  background: rgba(26, 42, 108, 0.8);
}

.rank-item .rank {
  width: 24px;
  height: 24px;
  background: rgba(26, 42, 108, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 12px;
}

.rank-item .name {
  flex: 1;
  font-size: 14px;
}

.rank-item .value {
  font-size: 14px;
  color: #ffd700;
}

.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #a7c7ff;
  font-size: 14px;
}

.dashboard-footer {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #7f9eb2;
  /* background: rgba(0, 0, 0, 0.2); */
}
.datalist {
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.datalistt {
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 50%;
}

.scroll-container {
  width: 100%;
  box-sizing: border-box;
  background-image: url('@/assets/daping/btl.png');
  padding-left: 40px;
  background-size: 100%;
  background-repeat: no-repeat;
  margin-bottom: 25px;
  line-height: 40px;
  height: 40px;
  box-sizing: border-box;
}
.scroll-containeryuj {
  width: 100%;
  box-sizing: border-box;
  background-image: url('@/assets/daping/btl.png');
  padding-left: 60px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-bottom: 15px;
  line-height: 45px;
  height: 45px;
  box-sizing: border-box;
}
.echartsleft {
  flex: 1;
  box-sizing: border-box;
}

.scroll-content {
  transition: transform 0.3s ease;
  height: 100%;
  box-sizing: border-box;
}

.scroll-item {
  // height: 60px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.scroll-item:hover {
  background-color: #f5f5f5;
}
.rank {
  font-weight: bold;
}
.name {
  flex: 1;
  font-size: 16px;
}

.value {
  text-align: right;
  font-weight: bold;
}

.growth {
  width: 120px;
  text-align: right;
  color: #52c41a;
}
.rank-item-name {
  color: #fff;
}
.typebox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  // background: radial-gradient(circle, #00040e 50%, #ffc230 100%);

  background-image: url('@/assets/daping/headtitle.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 6px;
  height: 60px;
  margin-bottom: 10px;
  border: 1px dashed #655527; /* 金黄色虚线 */
  .typebox_item {
    width: 50%;
    height: 100%;
    // line-height: 48px;
    padding: 10px;
    font-size: 17px;
    text-align: center;
    cursor: pointer;
    font-weight: bold;
    span {
      margin-left: 20px;
    }
  }
  .activetpy {
    background-image: url('@/assets/daping/changetpye.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
::v-deep(.el-range-editor.el-input__wrapper) {
  width: 100%;
  // height: 42px;
  background: rgba(54, 156, 255, 0.2);
  border: none;
}
.demo-date-picker {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}

.demo-date-picker .block {
  padding: 30px 0;
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  flex: 1;
}

.demo-date-picker .block:last-child {
  border-right: none;
}

.demo-date-picker .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}
.typeboxlist {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  .data-row {
    font-family: Microsoft YaHei;
    flex: 1;
    align-items: center;
    font-size: 14px;
    color: #ffffff;
    border-left: 2px solid #1493ff;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    padding: 6px;
    // background: rgba(54, 156, 255, 0.2);
    margin: 5px 0px;
    box-sizing: border-box;
    background: linear-gradient(90deg, rgba(19, 126, 217, 0.6) 0%, rgba(19, 126, 217, 0) 100%);
    &:last-child {
      margin-bottom: 0px;
    }
    &:first-child {
      margin-top: 0px;
    }
  }
  .isactlisttype {
    // background: linear-gradient(90deg, #369cff 0%, rgba(54, 156, 255, 0) 100%);
    background: linear-gradient(90deg, rgba(255, 194, 48, 0.8) 0%, rgba(255, 194, 48, 0.01) 100%);
    color: #ffffff;
    border-left: 2px solid #ffc230;
  }
}
.headbox {
  background-image: url('@/assets/daping/headbg.png');
  background-repeat: no-repeat;
  // height: 180px;
  width: 100%;
  background-size: 100% 100%;
  display: flex;
  justify-content: space-between;
  padding: 11px 130px;
}
.headboxsub {
  width: 300px;
  text-align: center;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #5caee1;
}
.pjname {
  font-family: HYLingXinJ;
  font-weight: bold;
  font-size: 36px;
  color: #ffffff;
  background: linear-gradient(0deg, #82c1f7 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
}
.projectbox {
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px);
  margin-top: 4px;
  .dbg {
    // background: rgba(54, 156, 255, 0.1);
  }

  .projectitem {
    height: calc((100% / 10));
    cursor: pointer;
    overflow: hidden;
    width: 100%;
    display: flex;
    white-space: nowrap;
    justify-content: left;
    overflow: hidden;
    box-sizing: border-box;
    align-items: center;
    font-size: 15px;
    color: #fefefe;
    border-radius: 21px 0px 0px 21px;
    margin: 1px 0px;
    background: linear-gradient(90deg, rgba(54, 156, 255, 0) 0%, rgba(54, 156, 255, 0) 100%);
    &:nth-child(1) {
      background: linear-gradient(90deg, rgba(232, 56, 77, 0.8) 0%, rgba(232, 56, 77, 0) 100%) !important;
    }
    &:nth-child(2) {
      background: linear-gradient(90deg, rgba(255, 157, 35, 0.8) 0%, rgba(255, 157, 35, 0) 100%) !important;
    }
    &:nth-child(3) {
      background: linear-gradient(90deg, rgba(255, 216, 62, 0.8) 0%, rgba(255, 216, 62, 0) 100%) !important;
    }
    .num {
      margin: 0px 14px;
      display: inline-block;
      width: 48px;
      height: 18px;
      border-radius: 9px;
      text-align: center;
      line-height: 18px;
      font-family: Arial;
      font-weight: 400;
      color: #fefefe;
    }
    &:nth-child(1) .num {
      background: linear-gradient(90deg, #e8384d 0%, rgba(232, 56, 77, 0) 100%);
    }
    &:nth-child(2) .num {
      background: linear-gradient(90deg, #ff9d23 0%, rgba(255, 156, 35, 0) 100%);
    }
    &:nth-child(3) .num {
      background: linear-gradient(90deg, #ffd83e 0%, rgba(255, 216, 62, 0) 100%);
    }
    // .top1 {
    //   background: linear-gradient(90deg, #e8384d 0%, rgba(232, 56, 77, 0.29) 100%);
    // }
    // .top2 {
    //   background: linear-gradient(90deg, #ff9d23 0%, rgba(255, 156, 35, 0.29) 100%);
    // }
    // .activepj {
    //   background: linear-gradient(90deg, rgba(20, 147, 255, 0.8) 0%, rgba(117, 212, 255, 0.1) 100%);
    // }
  }
  .currentpjbg {
    background: linear-gradient(90deg, rgba(20, 147, 255, 0.8) 0%, rgba(117, 212, 255, 0.1) 100%) !important;
  }
  .currentpjbg1 {
    background: linear-gradient(90deg, rgba(232, 56, 77, 0.8) 0%, rgba(232, 56, 77, 0.1) 100%) !important;
  }
  .currentpjbg2 {
    background: linear-gradient(90deg, rgba(20, 147, 255, 0.8) 0%, rgba(117, 212, 255, 0.1) 100%) !important;
  }
  .currentpjbg3 {
    background: linear-gradient(90deg, rgba(20, 147, 255, 0.8) 0%, rgba(117, 212, 255, 0.1) 100%) !important;
  }
}

.left-panel {
  animation-name: slide;
  // animation-duration: 1.5s; // 新增
  /* ... */
  &:nth-child(1) {
    animation-duration: 0.8s; // 新增
  }
  &:nth-child(2) {
    animation-duration: 2s !important; // 新增
  }
}
.data-card {
  /* ... */
  animation-name: slide;
  &:nth-child(2) {
    animation-duration: 1.5s; // 新增
  }
}

@keyframes slide {
  0% {
    transform: translateX(-100%);
  }
  80% {
    transform: translateX(20px);
  }
  100% {
    transform: translateX(0);
  }
}

.right-panel {
  /* ... */
  animation-name: slider;
  &:nth-child(1) {
    animation-duration: 0.5s; // 新增
  }

  &:nth-child(3) {
    animation-duration: 1.5s; // 新增
  }
}
.datalistt {
  /* ... */
  animation-name: slider;
  &:nth-child(2) {
    animation-duration: 1.5s; // 新增
  }
}

@keyframes slider {
  0% {
    transform: translateX(100%);
  }
  80% {
    transform: translateX(-20px);
  }
  100% {
    transform: translateX(0);
  }
}
.map-area {
  animation: slideAndFade 1.5s; // 新增
}

@keyframes slideAndFade {
  0% {
    transform: translateY(218px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
.headbox {
  animation: fade 3s;
}
@keyframes fade {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.chulildata {
  font-size: 12px;
  font-weight: 100;
  // margin: 0px 5px;
  width: 70px;
}
::v-deep(.el-message-box) {
  background: linear-gradient(90deg, rgba(20, 147, 255, 0.8) 0%, rgba(117, 212, 255, 0.1) 100%) !important;
}
</style>
