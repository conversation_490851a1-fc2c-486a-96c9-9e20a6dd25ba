<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="施工许可证编号" prop="constructionPermitNo" label-width="120px">
              <el-input v-model="queryParams.constructionPermitNo" placeholder="请输入施工许可证编号" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="参建单位" prop="participateOrgName" label-width="80px">
              <el-input v-model="queryParams.participateOrgName" placeholder="请输入参建单位名称" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="所在地区" prop="areaSearch">
              <el-cascader v-model="searchSelectedArea" :options="areaOptions" :props="{
                checkStrictly: true,
                expandTrigger: 'click',
                value: 'id',
                label: 'name',
                children: 'children',
                multiple: false
              }" clearable filterable style="width: 100%" placeholder="请选择市/区县" @change="handleSearchAreaChange" />
            </el-form-item>
            <el-form-item label="项目状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择项目状态" clearable>
                <el-option v-for="dict in prj_projects_status" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <!--            <el-form-item label="项目日期范围" prop="params.dateRange">-->
            <!--              <el-date-picker-->
            <!--                v-model="dateRange"-->
            <!--                type="daterange"-->
            <!--                range-separator="至"-->
            <!--                start-placeholder="开始日期"-->
            <!--                end-placeholder="结束日期"-->
            <!--                value-format="YYYY-MM-DD"-->
            <!--                @change="handleDateRangeChange"-->
            <!--              ></el-date-picker>-->
            <!--            </el-form-item>-->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['projects:prj_projects:add']">
              手动录入 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Refresh" @click="handleSyncProject"
              v-hasPermi="['projects:prj_projects:sync']">
              同步项目
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['projects:prj_projects:edit']">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['projects:prj_projects:remove']">删除
            </el-button>
          </el-col>
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['projects:prj_projects:export']">导出</el-button>-->
          <!--          </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="prj_projectsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!--        <el-table-column label="项目ID" align="center" prop="projectId" v-if="true" />-->
        <el-table-column label="项目名称" align="center" prop="projectName">
          <template #default="scope">
            <div class="prodetail" style="cursor: pointer" @click="handleViewDetail(scope.row)">{{ scope.row.projectName
            }}</div>
          </template>
        </el-table-column>
        <el-table-column label="项目编码/标识" align="center" prop="projectCode" />
        <!--        <el-table-column label="工程概况" align="center" prop="projectOverview" show-overflow-tooltip />-->
        <el-table-column label="施工许可证编号" align="center" prop="constructionPermitNo" />
        <el-table-column label="所在地区" align="center">
          <template #default="scope">
            {{ [scope.row.provinceName, scope.row.cityName, scope.row.districtName].filter(Boolean).join('/') || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="项目位置" align="center" prop="locationDetail">
          <template #default="scope">
            <el-link :href="'https://uri.amap.com/marker?position=' + scope.row.lola" target="_blank">
              {{ scope.row.locationDetail }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="项目状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="prj_projects_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="计划开工日期" align="center" prop="startDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划竣工日期" align="center" prop="plannedEndDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.plannedEndDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; gap: 4px;">
              <!-- 常用操作按钮 -->
              <el-tooltip content="详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleViewDetail(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['projects:prj_projects:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="项目人员" placement="top">
                <el-button link type="primary" icon="User" @click="handleUserProject(scope.row)"></el-button>
              </el-tooltip>

              <!-- 更多操作下拉菜单 -->
              <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, scope.row)">
                <el-button link type="primary" icon="ArrowDown">
                  更多
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="memo" icon="Memo"
                      v-hasPermi="['safeTask:safeTask:list']">安拆任务</el-dropdown-item>
                    <el-dropdown-item command="monitor" icon="Monitor">塔吊监控</el-dropdown-item>
                    <el-dropdown-item command="management" icon="Management">工程管理</el-dropdown-item>
                    <el-dropdown-item command="special" icon="Tools"
                      v-if="checkPermi(['system:special:information'])">特种信息</el-dropdown-item>
                    <el-dropdown-item command="bind" icon="UserFilled" divided
                      v-if="checkPermi(['projects:prj_projects:bindAdmin'])">绑定项目管理员</el-dropdown-item>
                    <el-dropdown-item command="delete" icon="Delete" divided
                      v-if="checkPermi(['projects:prj_projects:remove'])" style="color: #f56c6c;">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改项目录入对话框 -->
    <el-dialog :title="dialog.title" :show-close="false" v-model="dialog.visible" width="90vw" append-to-body>
      <el-form ref="prj_projectsFormRef" :model="form" :rules="rules" label-width="150px">
        <el-divider content-position="left">基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属监督站" prop="supervisingQsOrgId">
              <el-input v-model="form.supervisingQsOrgName" placeholder="请输入所属监督站" readonly>
                <template #append>
                  <el-button-group>
                    <el-button @click="selectOrganization('GOV_QS', 'supervisingQsOrgId', 'supervisingQsOrgName')"> 选择
                    </el-button>
                  </el-button-group>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码/标识" prop="projectCode">
              <el-input v-model="form.projectCode" placeholder="请输入项目编码/标识" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工程概况" prop="projectOverview">
          <el-input v-model="form.projectOverview" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <!--<el-form-item label="施工许可证编号" prop="constructionPermitNo">
                  <el-input v-model="form.constructionPermitNo" placeholder="请输入施工许可证编号" />
                </el-form-item>-->
        <el-form-item label="施工许可证编号" prop="constructionPermitNo">
          <el-input v-model="form.constructionPermitNo" placeholder="请输入施工许可证编号" />
        </el-form-item>
        <el-form-item label="施工许可证扫描件" prop="constructionPermitDocId">
          <file-upload :model-value="form.constructionPermitDocId ? String(form.constructionPermitDocId) : ''"
            @update:model-value="form.constructionPermitDocId = $event" :limit="1" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="占地面积(平方米)" prop="siteArea">
              <el-input-number v-model="form.siteArea" :min="0" :precision="2" :step="10" controls-position="right"
                placeholder="请输入占地面积" style="width: 100%">
                <template #append>平方米</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预算投资总额(万元)" prop="budgetTotal">
              <el-input-number v-model="form.budgetTotal" :min="0" :precision="2" :step="100" controls-position="right"
                placeholder="请输入预算投资总额" style="width: 100%">
                <template #append>万元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">地区信息</el-divider>
        <el-form-item label="搜索位置" prop="lola">
          <div class="latLong">
            <GdMap v-if="isDestroy" ref="gdmap" :coordinate="coordinate" @setLocation="setLocation"></GdMap>
          </div>
        </el-form-item>
        <el-form-item label="所在地区" prop="area">
          <el-cascader v-model="selectedArea" :options="areaOptions" :props="{
            checkStrictly: true,
            expandTrigger: 'click',
            value: 'id',
            label: 'name',
            children: 'children',
            multiple: false
          }" clearable filterable style="width: 100%" placeholder="市/区县" @change="handleAreaChange"
            :disabled="true" />
        </el-form-item>
        <el-form-item label="详细地址" prop="locationDetail">
          <el-input v-model="form.locationDetail" type="textarea" placeholder="请输入内容" />
        </el-form-item>

        <el-divider content-position="left">状态与日期</el-divider>
        <el-form-item label="项目状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in prj_projects_status" :key="dict.value" :value="dict.value">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划开工日期" prop="startDate">
              <el-date-picker clearable v-model="form.startDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择计划开工日期" style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划竣工日期" prop="plannedEndDate">
              <el-date-picker clearable v-model="form.plannedEndDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择计划竣工日期" style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实际开工日期" prop="actualStartDate">
              <el-date-picker clearable v-model="form.actualStartDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择实际开工日期" style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际竣工日期" prop="actualEndDate">
              <el-date-picker clearable v-model="form.actualEndDate" type="date" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择实际竣工日期" style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">相关单位</el-divider>
        <el-form-item label="建设单位" prop="clientOrgId">
          <el-input v-model="form.clientOrgName" placeholder="请输入建设单位" readonly>
            <template #append>
              <el-button-group>
                <el-button @click="selectOrganization('CLIENT', 'clientOrgId', 'clientOrgName')">选择</el-button>
                <el-button :disabled="form.clientOrgId == undefined" @click="selectQualification(form.clientOrgId)"
                  style="border-left: 1px solid #dcdfe6">人员维护
                </el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="施工单位" prop="constructionOrgId">
          <el-input v-model="form.constructionOrgName" placeholder="请输入施工单位" readonly :disabled="isConstructionUnit()">
            <template #append>
              <el-button-group>
                <el-button @click="selectOrganization('CONSTRUCTION', 'constructionOrgId', 'constructionOrgName')"
                  :disabled="isConstructionUnit()">选择
                </el-button>
                <el-button :disabled="form.constructionOrgId == undefined"
                  @click="selectQualification(form.constructionOrgId)" style="border-left: 1px solid #dcdfe6">人员维护
                </el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="监理单位" prop="supervisionOrgId">
          <el-input v-model="form.supervisionOrgName" placeholder="请输入监理单位" readonly>
            <template #append>
              <el-button-group>
                <el-button @click="selectOrganization('SUPERVISION', 'supervisionOrgId', 'supervisionOrgName')">选择
                </el-button>
                <el-button :disabled="form.supervisionOrgId == undefined"
                  @click="selectQualification(form.supervisionOrgId)" style="border-left: 1px solid #dcdfe6">人员维护
                </el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="设计单位" prop="designOrgId">
          <el-input v-model="form.designOrgName" placeholder="请输入设计单位" readonly>
            <template #append>
              <el-button-group>
                <el-button @click="selectOrganization('DESIGN', 'designOrgId', 'designOrgName')">选择</el-button>
                <el-button :disabled="form.designOrgId == undefined" @click="selectQualification(form.designOrgId)"
                  style="border-left: 1px solid #dcdfe6">人员维护
                </el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="勘察单位" prop="surveyOrgId">
          <el-input v-model="form.surveyOrgName" placeholder="请输入勘察单位" readonly>
            <template #append>
              <el-button-group>
                <el-button @click="selectOrganization('SURVEY', 'surveyOrgId', 'surveyOrgName')">选择</el-button>
                <el-button :disabled="form.surveyOrgId == undefined" @click="selectQualification(form.surveyOrgId)"
                  style="border-left: 1px solid #dcdfe6">人员维护
                </el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="安拆单位" prop="installationDismantlingOrgId">
          <el-input v-model="form.installationDismantlingOrgName" placeholder="请输入安拆单位" readonly>
            <template #append>
              <el-button-group>
                <el-button
                  @click="selectOrganization('INSTALLATION_DISMANTLING', 'installationDismantlingOrgId', 'installationDismantlingOrgName')">选择</el-button>
                <el-button :disabled="form.installationDismantlingOrgId == undefined"
                  @click="selectQualification(form.installationDismantlingOrgId)"
                  style="border-left: 1px solid #dcdfe6">人员维护
                </el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="维保单位" prop="maintenanceOrgId">
          <el-input v-model="form.maintenanceOrgName" placeholder="请输入维保单位" readonly>
            <template #append>
              <el-button-group>
                <el-button
                  @click="selectOrganization('MAINTENANCE', 'maintenanceOrgId', 'maintenanceOrgName')">选择</el-button>
                <el-button :disabled="form.maintenanceOrgId == undefined"
                  @click="selectQualification(form.maintenanceOrgId)" style="border-left: 1px solid #dcdfe6">人员维护
                </el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item>
        <div v-if="form.projectId">
          <el-divider content-position="left">相关人员</el-divider>
          <el-table stripe :data="form.personnelList" border>
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="idCard" label="证件号码" width="180" />
            <el-table-column prop="roleOnProject" label="角色类型" width="180">
              <template #default="scope">
                <dict-tag :options="personnel_position" :value="scope.row.roleOnProject" />
              </template>
            </el-table-column>

            <el-table-column prop="phone" label="手机号码" width="120" />
            <el-table-column prop="enterpriseName" label="所在单位" />
            <el-table-column label="性别" align="center" prop="gender" width="80px">
              <template #default="scope">
                <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
              </template>
            </el-table-column>
            <el-table-column label="文化程度" align="center" prop="education" width="120px">
              <template #default="scope">
                <dict-tag :options="educational_level_code" :value="scope.row.education" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" prop="education" width="100px">
              <template #default="scope">
                <el-button type="text" @click="delperson(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        >>>>>>> f28a7ba05a95bd3759b0ec50cba1587607b2f8c7
        <!-- 添加人员预览 -->
        <el-divider content-position="left" v-if="personnelPreviewList?.length > 0">添加人员预览</el-divider>

        <div v-if="personnelPreviewList?.length > 0">
          <el-table stripe :data="personnelPreviewList" border>
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="idCard" label="证件号码" width="180" />
            <el-table-column prop="roleOnProject" label="角色类型" width="180">
              <template #default="scope">
                <dict-tag :options="personnel_position" :value="scope.row.roleOnProject" />
              </template>
            </el-table-column>

            <el-table-column prop="phone" label="手机号码" width="120" />
            <el-table-column prop="enterpriseName" label="所在单位" />
            <el-table-column label="性别" align="center" prop="gender" width="80px">
              <template #default="scope">
                <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
              </template>
            </el-table-column>
            <el-table-column label="文化程度" align="center" prop="education" width="120px">
              <template #default="scope">
                <dict-tag :options="educational_level_code" :value="scope.row.education" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" prop="education" width="100px">
              <template #default="scope">
                <el-button type="text" @click="delpreviewperson(scope.$index, scope.row)">取消添加</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 项目详情弹窗 -->
    <el-dialog title="项目详情" v-model="detailDialog.visible" width="90vw" style="height: 90vh; overflow-y: auto"
      append-to-body>
      <el-card style="margin-bottom: 10px">
        <div class="basicinfo">
          <!-- <div class="ercode">
            <img src="" alt="" width="150" height="150" />
            <div>项目二维码下载</div>
          </div> -->
          <!-- 基本信息 -->
          <div class="basicinfo-left">
            <div class="basicinfotitle">
              <div class="gang"></div>
              <div class="basicinfoname">{{ detailForm.projectName }}</div>
            </div>
            <el-descriptions label-width="140px" :column="3" border>
              <el-descriptions-item label="项目编码">{{ detailForm.projectCode }}</el-descriptions-item>
              <el-descriptions-item label="工程概况" :span="2">{{ detailForm.projectOverview }}</el-descriptions-item>
              <el-descriptions-item label="施工许可证编号">{{ detailForm.constructionPermitNo }}</el-descriptions-item>
              <el-descriptions-item label="占地面积" v-if="detailForm.siteArea">{{ detailForm.siteArea }}
                平方米
              </el-descriptions-item>
              <el-descriptions-item label="预算投资总额" v-if="detailForm.budgetTotal">{{ detailForm.budgetTotal }}
                万元
              </el-descriptions-item>
              <el-descriptions-item label="项目位置地图">
                {{ detailForm.lola }}
                <el-button v-if="detailForm.lola" type="primary" link @click="viewMap(detailForm.lola)">查看</el-button>
              </el-descriptions-item>
              <el-descriptions-item label="所在地区">
                {{ detailForm.provinceName || '-' }} {{ detailForm.cityName ? '/ ' + detailForm.cityName : '' }}
                {{ detailForm.districtName ? '/ ' + detailForm.districtName : '' }}
              </el-descriptions-item>
              <el-descriptions-item label="详细地址">{{ detailForm.locationDetail || '-' }}</el-descriptions-item>
              <el-descriptions-item label="项目状态">
                <dict-tag :options="prj_projects_status" :value="detailForm.status" />
              </el-descriptions-item>
              <el-descriptions-item label="计划工期">
                {{ parseTime(detailForm.startDate, '{y}-{m}-{d}') || '-' }} 至
                {{ parseTime(detailForm.plannedEndDate, '{y}-{m}-{d}') || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="实际工期" v-if="detailForm.actualStartDate">
                {{ parseTime(detailForm.actualStartDate, '{y}-{m}-{d}') || '-' }} 至
                {{ parseTime(detailForm.actualEndDate, '{y}-{m}-{d}') || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="施工许可证扫描件" v-if="detailForm.constructionPermitDocId">
                <div @click="viewfile(detailForm.constructionPermitDocId)" style="cursor: pointer; color: #409eff">点击查看
                </div>
                <!-- <file-upload
                  style="width: 300px"
                  :model-value="detailForm.constructionPermitDocId ? String(detailForm.constructionPermitDocId) : ''"
                  @update:model-value="detailForm.constructionPermitDocId = $event"
                  disabled
                /> -->
              </el-descriptions-item>
              <el-descriptions-item label="所属监督站">{{ detailForm.supervisingQsOrgName || '-' }} </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>
      <!-- 参建单位 -->
      <el-card style="margin-bottom: 10px">
        <div slot="header">
          <div class="punits">
            <div class="basicinfotitle">
              <div class="gang"></div>
              <div class="basicinfoname">参建单位</div>
            </div>
          </div>
        </div>

        <!-- 单位信息 -->
        <el-table stripe :data="detailForm.enterpriseList" border style="width: 100%">
          <el-table-column prop="enterpriseType" label="企业类型">
            <template #default="scope">
              <!--              <div v-for="item in enterprise_type" :key="item.value">-->
              <!--                <span v-if="item.value === scope.row.enterpriseType">{{ item.label }}</span>-->
              <!--              </div>-->

              <dict-tag :options="enterprise_type" :value="scope.row.enterpriseType" />
            </template>
          </el-table-column>
          <el-table-column prop="enterpriseName" label="单位名称" min-width="180" />
          <el-table-column prop="unifiedSocialCreditCode" label="统一社会信用代码" />
          <el-table-column prop="legalRepresentative" label="法人代表" />
          <el-table-column prop="officePhone" label="办公电话" min-width="120" />
        </el-table>
      </el-card>

      <!-- 相关人员 -->
      <el-card style="margin-bottom: 10px" v-if="detailForm.personnelList && detailForm.personnelList.length > 0">
        <div slot="header">
          <div class="punits">
            <div class="basicinfotitle">
              <div class="gang"></div>
              <div class="basicinfoname">相关人员</div>
            </div>
          </div>
        </div>

        <!-- 人员信息 -->
        <el-table stripe :data="detailForm.personnelList" border style="width: 100%">
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="idCard" label="证件号码" width="180" />
          <el-table-column prop="roleOnProject" label="角色类型" width="180">
            <template #default="scope">
              <dict-tag :options="personnel_position" :value="scope.row.roleOnProject" />
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号码" width="120" />
          <el-table-column prop="enterpriseName" label="所在单位" />
          <el-table-column label="性别" align="center" prop="gender" width="80px">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="文化程度" align="center" prop="education" width="120px">
            <template #default="scope">
              <dict-tag :options="educational_level_code" :value="scope.row.education" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>

    </el-dialog>
    <!--  工作人员名单  -->
    <el-dialog title="工作人员名单" v-model="workerDialog" width="80vw" append-to-body>
      <el-button type="primary" :disabled="selTempUser.length <= 0" plain icon="Plus"
        @click="userIssued(selTempUser, '1')" style="margin-bottom: 15px">批量下发人脸
      </el-button>
      <el-table stripe :data="personnelList" border style="width: 100%" @selection-change="selectionWorkerChange">
        <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable" />
        <el-table-column prop="name" label="姓名" width="100" align="center" />
        <el-table-column prop="idCard" label="证件号码" width="180" />
        <el-table-column prop="roleOnProject" label="角色类型" width="180">
          <template #default="scope">
            <dict-tag :options="personnel_position" :value="scope.row.roleOnProject" />
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号码" width="120" />
        <el-table-column prop="enterpriseName" label="所在单位" />
        <el-table-column label="性别" align="center" prop="gender" width="80px">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="考勤设备" align="center" prop="education">
          <template #default="scope">
            <div v-if="scope.row.mattSnVos">
              <el-tag v-for="item in scope.row.mattSnVos" style="margin-right: 5px" type="primary">{{ item.snName }}
              </el-tag>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <!-- 考勤详情 -->
        <el-table-column label="操作" align="center" prop="education">
          <template #default="scope">
            <el-button type="text" @click="userIssued(scope.row, '0')">下发人脸</el-button>
            <el-button type="text" :disabled="scope.row.mattSnVos.length <= 0" @click="removeUserIssueds(scope.row)">
              清空人脸 </el-button>
            <el-button type="text" @click="signindetail(scope.row)">考勤记录</el-button>
            <el-button type="text" @click="delperson(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!--  下发人脸弹窗  -->
    <el-dialog title="打卡机列表" v-model="faceMachineDialog" width="500px" append-to-body>
      <el-select v-model="faceMachine" multiple placeholder="请选择打卡机" @change="handleMachineChange">
        <el-option v-for="item in attSnList" :key="item.sn" :label="item.snName" :value="item.sn"
          :disabled="item.disabled" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="subWait" :disabled="faceMachine.length <= 0" type="primary" @click="submitMachine">
            {{ subWait ? '下发中...' : '确 定' }}
          </el-button>
          <el-button @click="cancelMachine">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 选择单位对话框 -->
    <el-dialog :title="orgSelectDialog.title" v-model="orgSelectDialog.visible" fullscreen append-to-body>
      <el-form :inline="true" label-width="80px">
        <el-form-item label="单位类型" v-if="currentOrgType !== 'GOV_QS'">
          <el-select v-model="currentOrgType" placeholder="请选择单位类型" @change="handleOrgTypeChange">
            <el-option v-for="dict in sys_dept_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称">
          <el-input v-model="orgSearchName" placeholder="请输入单位名称" clearable @keyup.enter="searchOrg" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="searchOrg">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="orgLoading" :data="orgList" @row-click="handleOrgRowClick" highlight-current-row
        :row-class-name="orgRowClassName">
        <el-table-column type="index" width="50" />
        <el-table-column property="deptName" label="单位名称" show-overflow-tooltip />
        <el-table-column property="deptCode" label="统一社会信用代码" show-overflow-tooltip />
        <el-table-column property="deptType" label="单位类型">
          <template #default="scope">
            <dict-tag :options="sys_dept_type" :value="scope.row.deptType" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="80">
          <template #default="scope">
            <el-button type="primary" link @click.stop="quickSelectOrg(scope.row)"> 选择</el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitOrgSelect">确 定</el-button>
          <el-button @click="cancelOrgSelect">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选择相关人员弹窗 -->
    <el-dialog :title="personSelectDialog.title" v-model="personSelectDialog.visible" width="70vw" :show-close="false"
      style="min-width: 1000px; height: 80vh" append-to-body>
      <!-- 选择证书种类 -->
      <el-form ref="pqueryFormRef" :model="personparams" :inline="true">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="personparams.name" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="身份证号码" prop="idCard" label-width="82px">
          <el-input v-model="personparams.idCard" placeholder="请输入身份证号码" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="personparams.phone" placeholder="请输入手机号码" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="证书种类" prop="certificateType">
          <el-cascader ref="refHandle" popper-class="certCityNames" v-model="certSearchValue"
            :options="QualificationDictOptions" @change="handleSearchChange" :props="{
              expandTrigger: 'hover' as const,
              value: 'id',
              label: 'name',
              children: 'children',
              checkStrictly: true
            }" clearable placeholder="请选择证书类别" :popper-append-to-body="false"></el-cascader>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getpersonlist">搜索</el-button>
          <el-button icon="Refresh" @click="resetQueryperson">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 表格 -->
      <el-table v-loading="personLoading" row-key="personId" @selection-change="handleSelectionperson" height="40vh"
        :data="personList" highlight-current-row ref="multipleTableRef">
        <!-- 多选 -->
        <el-table-column type="selection" :reserve-selection="true" width="50" align="center" />
        <el-table-column label="姓名" align="center" prop="name" min-width="80px" show-overflow-tooltip />
        <el-table-column label="身份证号码" align="center" prop="idCard" min-width="130px" />
        <el-table-column label="手机号码" align="center" prop="phone" min-width="110px" />
        <el-table-column label="籍贯" align="center" prop="nativePlace" min-width="80px" />
        <el-table-column label="性别" align="center" prop="gender" min-width="80px">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="政治面貌" align="center" prop="politicalStatus" min-width="100px">
          <template #default="scope">
            <dict-tag :options="politics_status" :value="scope.row.politicalStatus" />
          </template>
        </el-table-column>
        <el-table-column label="文化程度" align="center" prop="education" min-width="100px">
          <template #default="scope">
            <dict-tag :options="educational_level_code" :value="scope.row.education" />
          </template>
        </el-table-column>
        <!-- 操作 -->
        <!-- <el-table-column label="操作" align="center" width="80">
          <template #default="scope">
            <el-button type="primary" link> 选择 </el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="personlisttotal" v-model:page="personparams.pageNum"
        v-model:limit="personparams.pageSize" @pagination="getpersonlist" />
      <!-- 底部 -->
      <div class="dialog-footer" style="text-align: right; margin-top: 40px">
        <!-- <el-button @click="dialog.visible = false" type="primary">上一步</el-button> -->
        <el-button @click="submitselect" type="primary">确认</el-button>
        <el-button @click="cancelroleselect">取消</el-button>
      </div>
    </el-dialog>
    <!-- 选择证书类别弹窗 -->
    <el-dialog :title="certSelectDialog.title" v-model="certSelectDialog.visible" width="70vw" style="height: 80vh"
      append-to-body>
      证书种类：
      <el-cascader ref="refHandle" popper-class="certCityNames" v-model="certSearchValue"
        :options="QualificationDictOptions" @change="handleSearchChange" :props="{
          expandTrigger: 'hover' as const,
          value: 'id',
          label: 'name',
          children: 'children',
          checkStrictly: true
        }" clearable placeholder="请选择证书类别" :popper-append-to-body="false"></el-cascader>
      <!-- 确认按钮 -->
      <el-button style="margin-left: 10px" type="primary" @click="searchevidence">确认</el-button>
    </el-dialog>

    <!-- 角色选择弹窗 -->
    <el-dialog title="角色选择" :show-close="false" v-model="rolesdialog" width="500px" append-to-body>
      <el-radio-group v-model="radioroles">
        <el-radio v-for="dict in personnel_position" :key="dict.value" :label="dict.label" :value="dict.value">
          {{ dict.label }}
        </el-radio>
      </el-radio-group>
      <div class="dialog-footer" style="text-align: right; margin-top: 40px">
        <el-button @click="submitselectrole" type="primary">确认</el-button>
        <el-button @click="rolesdialog = false">取消</el-button>
      </div>
    </el-dialog>

    <!-- 考勤对话框 -->
    <el-dialog :title="attendancedialogtitle" :show-close="false" fullscreen v-model="attendancedialog" width="70vw"
      style="height: 100vh" append-to-body>
      <div v-if="AttMonthlist" style="display: flex; position: relative; height: 70vh; overflow: hidden">
        <div style="position: relative; width: 60%">
          <div class="Calendarselect">
            <el-date-picker v-model="monthValue" type="month" style="width: 150px; height: 25px" @change="changeMonth"
              :clearable="false"></el-date-picker>
          </div>
          <el-calendar v-loading="calendarLoading" v-model="selectedDate">
            <template #date-cell="{ data }">
              <div style="width: 100%; height: 100%">
                <div style="font-size: 16px; font-weight: 600; margin-bottom: 10px"
                  :class="data.isSelected ? 'is-selected' : ''">
                  {{ data.day.split('-').slice(1).join('-') }}
                </div>
                <div v-for="(item, index) in AttMonthlist" :key="index">
                  <div class="detailinfo" v-if="data.day == item.date">
                    <div v-if="comparedate2(data.day) && comparedate(data.day)"
                      style="display: flex; align-items: center">
                      <div class="dian"
                        :class="item.attStatus == 0 ? 'normalefficacy' : item.attStatus == 2 ? 'loseefficacy' : 'absence'">
                      </div>
                      <div>{{ item.statusDescription }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-calendar>
        </div>

        <el-card style="width: 40%">
          <template #header>
            <div class="card-header" style="font-weight: 600; font-size: 16px; margin: 10px 0">
              <span v-if="selectedDate">{{ formatdate(selectedDate) }} 考勤记录</span>
              <span v-else>今日考勤记录</span>
              <el-popover class="box-item" title="考勤规则" :width="300" placement="bottom">
                <template #reference>
                  <el-icon style="margin-left: 6px">
                    <InfoFilled />
                  </el-icon>
                </template>
                <div v-if="attenRules">
                  <el-table :data="attenRules" border stripe>
                    <el-table-column prop="startTime" label="上班打卡" show-overflow-tooltip />
                    <el-table-column prop="endTime" label="下班打卡" show-overflow-tooltip />
                  </el-table>
                </div>
                <div v-else>-</div>
              </el-popover>
            </div>
          </template>
          <el-table v-loading="attendanceLoading" row-key="id" :data="attendanceList" highlight-current-row>
            <!-- <el-table-column prop="realName" label="姓名" /> -->
            <el-table-column prop="realTimeFace" label="考勤图片">
              <template #default="scope">
                <el-image style="width: 80px; height: 80px" :src="'data:image/jpeg;base64,' + scope.row.realTimeFace"
                  :preview-src-list="['data:image/jpeg;base64,' + scope.row.realTimeFace]" :preview-teleported="true" />
              </template>
            </el-table-column>
            <el-table-column prop="attTime" label="考勤时间" show-overflow-tooltip />
            <el-table-column prop="snName" label="设备名称" show-overflow-tooltip />
            <el-table-column prop="personType" label="人员类型" show-overflow-tooltip />
            <el-table-column prop="attResult" label="考勤结果" width="200" show-overflow-tooltip>
              <template #default="scope">
                <el-tag :type="scope.row.attResult == 0 ? 'primary' : scope.row.attResult == 1 ? 'danger' : 'info'">{{
                  scope.row.attResultName }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer" style="margin-top: 20px; margin-right: 30px">
          <el-button type="primary" @click="closeatt"> 关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 项目管理人员对话框 -->
    <el-dialog v-model="adminBindDialog.visible" :title="adminBindDialog.title" width="1000px" append-to-body>
      <!-- 管理人员列表 -->
      <div v-if="!showAddAdminForm">
        <div style="margin-bottom: 16px;">
          <el-button type="primary" icon="Plus" @click="showAddForm">新增项目管理员</el-button>
        </div>
        <el-table v-loading="adminListLoading" :data="adminList" border>
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="idCard" label="身份证号" width="180" />
          <el-table-column prop="phone" label="手机号" width="120" />
          <el-table-column prop="enterpriseName" label="所属单位" />
          <el-table-column prop="roleOnProject" label="角色" width="120">
            <template #default="scope">
              <dict-tag :options="personnel_position" :value="scope.row.roleOnProject" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button type="danger" link icon="Delete" @click="deleteAdmin(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增项目管理员表单 -->
      <div v-if="showAddAdminForm">
        <el-form ref="adminBindFormRef" :model="adminBindForm" :rules="adminBindRules" label-width="100px">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="adminBindForm.name" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="身份证号" prop="idCard">
            <el-input v-model="adminBindForm.idCard" placeholder="请输入身份证号" />
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="adminBindForm.phone" placeholder="请输入手机号" />
          </el-form-item>
          <el-form-item label="所属单位" prop="orgId">
            <el-select v-model="adminBindForm.orgId" placeholder="请选择所属单位" clearable filterable>
              <el-option v-for="dept in adminOrgList" :key="dept.deptId" :label="dept.displayLabel || dept.deptName"
                :value="dept.deptId" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAdminBind">{{ showAddAdminForm ? '返回' : '关闭' }}</el-button>
          <el-button v-if="showAddAdminForm" type="primary" @click="submitAdminBind"
            :loading="adminBindLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Prj_projects" lang="ts">
import {
  listPrj_projects,
  getPrj_projects,
  delPrj_projects,
  addPrj_projects,
  updatePrj_projects,
  getEnterpriseUserList,
  delpersonnel,
  listDept,
  getPersonAtt,
  getProUser,
  getPersonAttMonth,
  postUserIssued,
  removeUserIssued,
  getFaceMachine,
  sync_project,
  getProjectScreen,
  bindAdminQuick,
  getProjectAdmins
} from '@/api/projects/prj_projects';
import { listAttSn } from '@/api/attendance/attSn';
import { formatAreaId } from '@/api/system/area/index';
import { Prj_projectsVO, Prj_projectsQuery, Prj_projectsForm } from '@/api/projects/prj_projects/types';
import { DeptQuery } from '@/api/system/dept/types';
import { getAreaTreeGs } from '@/api/system/area';
import GdMap from '@/components/GdMap/index.vue';
import { useUserStore } from '@/store/modules/user';
import { checkPermi } from '@/utils/permission';

import { ElMessageBox, ElLoading } from 'element-plus';
import { listQualificationDict } from '@/api/person/qualificationDict/api';
import { QualificationDictVO } from '@/api/person/qualificationDict/types';
import { listByIds } from '@/api/system/oss';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { prj_projects_status } = toRefs<any>(proxy?.useDict('prj_projects_status'));
const { educational_level_code, sys_user_sex, politics_status, certificate_level } = toRefs<any>(
  proxy?.useDict('educational_level_code', 'sys_user_sex', 'politics_status', 'certificate_level')
);
const { sys_dept_type } = toRefs<any>(proxy?.useDict('sys_dept_type'));
const { personnel_position } = toRefs<any>(proxy?.useDict('personnel_position'));
const { enterprise_type } = toRefs<any>(proxy?.useDict('enterprise_type'));
// 新增证书弹框中的证书种类级联选择框的options配置
const QualificationDictOptions = ref<QualificationDictVO[]>([]);
// 存放证书类型的所有数据的数组
const qualificationDict = ref<QualificationDictVO[]>([]);

const prj_projectsList = ref<Prj_projectsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<string[]>([]);

const queryFormRef = ref<ElFormInstance>();
const pqueryFormRef = ref<ElFormInstance>();
const prj_projectsFormRef = ref<ElFormInstance>();

const activeTab = ref('basic');

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const detailDialog = reactive<DialogOption>({
  visible: false
});

const detailForm = reactive<Prj_projectsForm>({} as Prj_projectsForm);

const orgSelectDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const personSelectDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const certSelectDialog = reactive<DialogOption>({
  visible: false,
  title: '选择证书种类'
});

const adminBindDialog = reactive<DialogOption>({
  visible: false,
  title: '项目管理员'
});

const orgList = ref<any[]>([]);
const adminOrgList = ref<any[]>([]);
const adminList = ref<any[]>([]); // 项目管理员列表
const showAddAdminForm = ref(false); // 是否显示新增管理员表单
const adminListLoading = ref(false); // 管理员列表加载状态
const currentOrgType = ref('');
const currentTargetFieldId = ref('');
const currentTargetFieldName = ref('');

const dialogSelectedOrgId = ref<string | number>('');
const dialogSelectedOrgName = ref('');

const orgSearchName = ref('');
const orgLoading = ref(false);

const areaOptions = ref<any[]>([]);
const selectedArea = ref<any[]>([]);
const searchSelectedArea = ref<any[]>([]);

// 地图默认的经纬度
const coordinate = ref<{ lang: number; lat: number }>({ lang: 103.689287, lat: 36.110355 });
// 地图组件的ref
const gdmap = ref<InstanceType<typeof GdMap>>();
const isDestroy = ref(true);

let fileurl = ref('');
const viewfile = (id) => {
  listByIds(id).then((res) => {
    fileurl.value = res.data[0].url;
    window.open(fileurl.value);
  });
};
const initFormData: Prj_projectsForm = {
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  projectOverview: undefined,
  constructionPermitNo: undefined,
  constructionPermitDocId: undefined,
  lola: undefined,
  provinceCode: undefined,
  provinceName: undefined,
  cityCode: undefined,
  cityName: undefined,
  districtCode: undefined,
  districtName: undefined,
  countyCode: undefined,
  countyName: undefined,
  locationDetail: undefined,
  status: undefined,
  startDate: undefined,
  plannedEndDate: undefined,
  actualStartDate: undefined,
  actualEndDate: undefined,
  clientOrgId: undefined,
  clientOrgName: undefined,
  constructionOrgId: undefined,
  constructionOrgName: undefined,
  supervisionOrgId: undefined,
  supervisionOrgName: undefined,
  designOrgId: undefined,
  designOrgName: undefined,
  surveyOrgId: undefined,
  surveyOrgName: undefined,
  installationDismantlingOrgId: undefined,
  installationDismantlingOrgName: undefined,
  maintenanceOrgId: undefined,
  maintenanceOrgName: undefined,
  supervisingQsOrgId: undefined,
  subcontractorOrgIds: undefined,
  projectManagerUserId: undefined,
  supervisionChiefEngUserId: undefined,
  safetyMeasuresFeeDocId: undefined,
  siteArea: undefined,
  budgetTotal: undefined,
  personIds: undefined
};
const data = reactive<PageData<Prj_projectsForm, Prj_projectsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    constructionPermitNo: undefined,
    provinceCode: undefined,
    cityCode: undefined,
    districtCode: undefined,
    status: undefined,
    participateOrgName: undefined,
    params: {
      dateRange: []
    }
  },
  rules: {
    projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
    projectCode: [{ required: true, message: '项目编码/标识不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '项目状态不能为空', trigger: 'change' }],
    lola: [{ required: true, message: '所在地区不能为空', trigger: 'change' }],
    startDate: [{ required: true, message: '计划开工日期不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 头部搜索时的证书类型和证书名称的value
const handleSearchChange = (value: string[]) => {
  if (!value) {
    personparams.value.certificateName = undefined;
    personparams.value.certificateType = undefined;
    return;
  }
  personparams.value.certificateName = value[0];
  personparams.value.certificateType = value[1];
};
/** 日期范围变化处理 */
const handleDateRangeChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.value.params.dateRange = dateRange.value;
  } else {
    queryParams.value.params.dateRange = [];
  }
};

/** 处理同步项目按钮点击 */
const handleSyncProject = () => {
  ElMessageBox.prompt('请输入施工许可证编号', '同步项目信息', {
    confirmButtonText: '同步',
    cancelButtonText: '取消',
    inputPattern: /^[0-9A-Za-z]{10,}$/,
    inputErrorMessage: '施工许可证编号格式不正确，应为10位以上的数字和字母组合'
  })
    .then(({ value }) => {
      if (validatePermitNo(value)) {
        syncProjectByPermitNo(value.trim());
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

/** 验证施工许可证编号格式 */
const validatePermitNo = (permitNo: string): boolean => {
  if (!permitNo || permitNo.trim().length === 0) {
    proxy?.$modal.msgError('施工许可证编号不能为空');
    return false;
  }

  const trimmedPermitNo = permitNo.trim();

  if (trimmedPermitNo.length < 10) {
    proxy?.$modal.msgError('施工许可证编号长度不能少于10位');
    return false;
  }

  if (!/^[0-9A-Za-z]+$/.test(trimmedPermitNo)) {
    proxy?.$modal.msgError('施工许可证编号只能包含数字和字母');
    return false;
  }

  return true;
};

/** 根据施工许可证编号同步项目 */
const syncProjectByPermitNo = async (constructionPermitNo: string) => {
  if (!constructionPermitNo) {
    proxy?.$modal.msgError('施工许可证编号不能为空');
    return;
  }

  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在同步项目信息，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    const res = await sync_project(constructionPermitNo);
    proxy?.$modal.msgSuccess(`项目信息同步成功！项目名称：${res.data?.projectName || '未知'}`);
    await getList();
  } finally {
    loadingInstance?.close();
  }
};

/** 查询项目录入列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listPrj_projects(queryParams.value);
    prj_projectsList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取项目列表失败', error);
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  // 清除人员维护中的选项
  resetpersonparams();
  // 清空personIds.value
  personIds.value = null;
  dialog.visible = false;
  // 清空预览列表
  personnelPreviewList.value = null;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  selectedArea.value = [];
  dialogSelectedOrgId.value = '';
  dialogSelectedOrgName.value = '';
  prj_projectsFormRef.value?.resetFields();
  activeTab.value = 'basic';
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = [];
  searchSelectedArea.value = [];
  queryFormRef.value?.resetFields();
  queryParams.value.cityCode = ''
  queryParams.value.districtCode = ''
  queryParams.value.provinceCode = ''
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: Prj_projectsVO[]) => {
  ids.value = selection.map((item) => item.projectId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 判断当前登录用户是否属于施工单位类型
const isConstructionUnit = () => {
  const userStore = useUserStore();
  // 通过角色判断是否为施工单位
  return userStore.roles.some((role) => role === 'CONSTRUCTION');
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加项目录入';

  // 判断当前用户部门类型是否为施工单位
  const userStore = useUserStore();
  if (isConstructionUnit()) {
    form.value.constructionOrgId = userStore.deptId;
    form.value.constructionOrgName = userStore.deptName;
  }

  // 确保地图初始化
  isDestroy.value = false;
  nextTick(() => {
    isDestroy.value = true;
    nextTick(() => {
      // 确保组件已经挂载
      if (gdmap.value) {
        gdmap.value.initAMap(false);
      }
    });
  });
};

/** 修改按钮操作 */
const handleUpdate = async (row?: Prj_projectsVO) => {
  reset();
  const _projectId = row?.projectId || ids.value[0];
  loading.value = true;
  try {
    // 先确保地区树数据已加载
    if (areaOptions.value.length === 0) {
      await getArea();
    }
    // 获取项目数据
    const res = await getPrj_projects(_projectId);
    Object.assign(form.value, res.data);

    // 确保监督站信息被正确回显
    if (form.value.supervisingQsOrgId) {
      dialogSelectedOrgId.value = form.value.supervisingQsOrgId;
      dialogSelectedOrgName.value = form.value.supervisingQsOrgName || '';
    }

    // 重置并设置级联选择器的值
    selectedArea.value = [];

    // 如果有省级代码，先尝试直接使用它
    if (form.value.provinceCode) {
      try {
        const provinceId = parseInt(form.value.provinceCode);
        // 先检查此ID是否存在于地区树的顶层
        const provinceExists = areaOptions.value.some((p) => p.id === provinceId);

        if (provinceExists) {
          // 省份ID合法，添加到选中值
          selectedArea.value.push(provinceId);

          // 如果有市级代码，验证并添加
          if (form.value.cityCode) {
            const cityId = parseInt(form.value.cityCode);
            // 找到省份节点
            const province = areaOptions.value.find((p) => p.id === provinceId);
            // 检查市ID是否存在于该省的子节点中
            if (province && province.children && province.children.some((c) => c.id === cityId)) {
              selectedArea.value.push(cityId);

              // 如果有区县代码，验证并添加
              if (form.value.districtCode) {
                const districtId = parseInt(form.value.districtCode);
                // 找到市节点
                const city = province.children.find((c) => c.id === cityId);
                // 检查区县ID是否存在于该市的子节点中
                if (city && city.children && city.children.some((d) => d.id === districtId)) {
                  selectedArea.value.push(districtId);
                }
              }
            }
          }
        }
      } catch (e) {
        console.error('解析地区代码失败:', e);
        selectedArea.value = []; // 出错时重置
      }
    }

    // 确保地图初始化
    isDestroy.value = false;
    nextTick(() => {
      isDestroy.value = true;
      nextTick(() => {
        // 确保组件已经挂载
        if (gdmap.value && form.value.lola) {
          gdmap.value.lang = (form.value.lola as string).split(',')[0];
          gdmap.value.lat = (form.value.lola as string).split(',')[1];
          gdmap.value.initAMap(false);
        }
      });
    });

    dialog.visible = true;
    dialog.title = '修改项目录入';
  } catch (error) {
    console.error('获取项目详情失败', error);
    proxy?.$modal.msgError('获取项目详情失败');
  } finally {
    loading.value = false;
  }
};

/** 查看详情按钮操作 */
const handleViewDetail = async (row: Prj_projectsVO) => {
  loading.value = true;
  try {
    const res = await getPrj_projects(row.projectId);
    Object.assign(detailForm, res.data);

    detailDialog.visible = true;
  } catch (error) {
    console.error('获取项目详情失败', error);
    proxy?.$modal.msgError('获取项目详情失败');
  } finally {
    loading.value = false;
  }
};

let personnelList = ref([]);
let workerDialog = ref(false);
let tempRow = ref(null);
/** 项目人员*/
const handleUserProject = async (row: Prj_projectsVO) => {
  tempRow.value = row;
  try {
    const res = await getProUser(row.projectId);
    personnelList.value = res.data;
    workerDialog.value = true;
  } catch (error) {
    console.error('获取项目人员失败', error);
    proxy?.$modal.msgError('获取项目人员失败');
  }
};
// 安拆任务按钮点击事件
const handleSafeTask = async (row: Prj_projectsVO) => {
  proxy?.$tab.openPage(`/prj/project_safe_task/${row.projectId}`);
};
// 塔吊监控按钮事件方法
const handleTowerCraneMonitor = async (row: Prj_projectsVO) => {
  const res = await getProjectScreen(row.projectId);
  if (res.code == 200) {
    window.open(res.data, '_blank');
  }
};
/** 提交按钮 */
const submitForm = () => {
  prj_projectsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        form.value.personIds = personIds.value;
        if (form.value.projectId) {
          await updatePrj_projects(form.value);
        } else {
          await addPrj_projects(form.value);
        }
        proxy?.$modal.msgSuccess('操作成功');
        resetpersonparams();
        dialog.visible = false;
        personIds.value = null;
        personnelPreviewList.value = null;
        await getList();
      } catch (error) {
        console.error('保存项目失败', error);
        proxy?.$modal.msgError('操作失败');
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: Prj_projectsVO) => {
  const _projectIds = row?.projectId || ids.value;
  try {
    await proxy?.$modal.confirm('是否确认删除项目录入编号为"' + _projectIds + '"的数据项？');
    loading.value = true;
    await delPrj_projects(_projectIds);
    proxy?.$modal.msgSuccess('删除成功');
    await getList();
  } catch (error) {
    console.error('删除项目失败', error);
  } finally {
    loading.value = false;
  }
};

/** 处理下拉菜单命令 */
const handleDropdownCommand = (command: string, row: Prj_projectsVO) => {
  switch (command) {
    case 'bind':
      handleBindPersonnel(row);
      break;
    case 'memo':
      handleSafeTask(row);
      break;
    case 'monitor':
      handleTowerCraneMonitor(row);
      break;
    case 'management':
      handleProjectManagement(row, 1);
      break;
    case 'special':
      handleProjectManagement(row, 2);
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      break;
  }
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'projects/prj_projects/export',
    {
      ...queryParams.value
    },
    `prj_projects_${new Date().getTime()}.xlsx`
  );
};

/** 选择单位 */
const selectOrganization = async (orgType: string, targetFieldId: string, targetFieldName: string) => {
  // 如果是施工单位类型，且当前用户是施工单位，则不允许选择
  if (orgType === 'CONSTRUCTION' && isConstructionUnit()) {
    proxy?.$modal.msgWarning('不允许修改施工单位');
    return;
  }

  // 如果是选择监督站，但表单中没有cityCode，提示用户先选择地区信息
  if (orgType === 'GOV_QS' && !form.value.cityCode) {
    proxy?.$modal.msgWarning('请先选择地区信息');
    return;
  }

  currentOrgType.value = orgType;
  currentTargetFieldId.value = targetFieldId;
  currentTargetFieldName.value = targetFieldName;

  const existingOrgId = (form.value as any)[targetFieldId];
  const existingOrgName = (form.value as any)[targetFieldName];
  if (existingOrgId) {
    dialogSelectedOrgId.value = existingOrgId;
    dialogSelectedOrgName.value = existingOrgName || '';
  } else {
    dialogSelectedOrgId.value = '';
    dialogSelectedOrgName.value = '';
  }

  orgSearchName.value = '';
  orgSelectDialog.visible = true;

  // 如果是选择监督站，则设置特殊标题，隐藏单位类型选择
  if (orgType === 'GOV_QS') {
    orgSelectDialog.title = '选择所属监督站';
  } else {
    orgSelectDialog.title = '选择' + getOrgTypeLabel(orgType);
  }

  await searchOrg();
};
const personList = ref([]);
const personLoading = ref(false);

let personparams = ref({
  pageSize: 10,
  pageNum: 1,
  deptId: null,
  name: undefined,
  idCard: undefined,
  phone: undefined,
  nativePlace: undefined,
  gender: undefined,
  politicalStatus: undefined,
  education: undefined,
  certificateType: undefined,
  certificateName: undefined
});
const resetpersonparams = () => {
  personparams.value = {
    pageSize: 10,
    pageNum: 1,
    deptId: null,
    name: undefined,
    idCard: undefined,
    phone: undefined,
    nativePlace: undefined,
    gender: undefined,
    politicalStatus: undefined,
    education: undefined,
    certificateType: undefined,
    certificateName: undefined
  };
  selectedData.value = null;
  multipleTableRef.value?.clearSelection(); // 调用表格的 clearSelection 方法
  // 清空角色选择
  radioroles.value = null;
  // 清空人员预览列表
  // personnelPreviewList.value = null;
};

const checkSelectable = (row) => {
  if (row.disable) {
    return false;
  }
  return true;
};

const certSearchValue = ref();
let personlisttotal = ref(0);
// 点击人员维护
const selectQualification = (enterpriseId) => {
  // certSearchValue.value = undefined;

  if (enterpriseId) {
    personparams.value.deptId = enterpriseId;
    getQualificationDict();
    personSelectDialog.visible = true;
    selectperson();
  } else {
    // 提示
    proxy?.$modal.msgError('请先选择单位');
  }
};
// 确认选择证书,
const searchevidence = () => {
  // 判断证书选择
  if (certSearchValue.value) {
    selectperson();
  } else {
    proxy?.$modal.msgError('请选择证书种类');
  }
};
// 添加人员预览列表
let personnelPreviewList = ref([]);
const selectperson = () => {
  personSelectDialog.visible = true;
  personSelectDialog.title = '选择人员';
  personLoading.value = true;
  // 弹窗显示
  // 人员列表查询
  getEnterpriseUserList(personparams.value).then((Res) => {
    personLoading.value = false;
    personList.value = Res.rows;
    personlisttotal.value = Res.total;
  });
};

/** 重置按钮操作 */
const resetQueryperson = () => {
  personparams.value.certificateName = undefined;
  personparams.value.certificateType = undefined;
  pqueryFormRef.value?.resetFields();
  getpersonlist();
};

// 选择人员
let selectedData = ref(null);

// 人员数据
const handleSelectionperson = (val) => {
  selectedData.value = val; // 获取选中数据
};
//
const multipleTableRef = ref();
// 角色弹窗
let rolesdialog = ref(false);
// 角色
let radioroles = ref(null);
// 点击确认选择人员
const submitselect = () => {
  // 判断有没有选人
  if (selectedData.value && selectedData.value.length > 0) {
    // 选择角色
    rolesdialog.value = true;
  } else {
    proxy?.$modal.msgError('请先选择人员');
  }
};
let personIds = ref(null);
// 点击确认角色
const submitselectrole = async () => {
  if (radioroles.value) {
    // 转为数组，
    let arrlist = [];
    if (personIds.value) {
      arrlist = personIds.value.split('|');
    }
    let rolelist = selectedData.value;
    // 先拼角色属性，然后用concat组成新数组
    await rolelist.forEach((item) => {
      item.roleOnProject = radioroles.value;
    });
    if (personnelPreviewList.value) {
      personnelPreviewList.value = personnelPreviewList.value.concat(selectedData.value);
    } else {
      personnelPreviewList.value = selectedData.value;
    }

    // 将新的拼接好，然后转为字符串中间用"|"分割
    let arrperson = [];
    await selectedData.value.forEach((item) => {
      arrperson.push(`${item.personId},${radioroles.value},${personparams.value.deptId}`);
    });
    arrperson = arrlist.concat(arrperson);
    personIds.value = arrperson.join('|');

    // 关闭弹窗
    personSelectDialog.visible = false;
    rolesdialog.value = false;
    // 清空角色选择，人员选择
    selectedData.value = null;
    multipleTableRef.value?.clearSelection(); // 调用表格的 clearSelection 方法
    radioroles.value = null;
  } else {
    proxy?.$modal.msgError('请选择角色');
  }
};

// 提交项目管理员绑定
const submitAdminBind = async () => {
  if (!adminBindFormRef.value) return;

  const valid = await adminBindFormRef.value.validate().catch(() => false);
  if (!valid) return;

  if (!tempBindProjectId.value) {
    proxy?.$modal.msgError('项目ID不能为空');
    return;
  }

  adminBindLoading.value = true;
  try {
    await bindAdminQuick(tempBindProjectId.value, adminBindForm.value);
    proxy?.$modal.msgSuccess('绑定成功');

    // 重置表单并返回列表页面
    adminBindForm.value = {
      name: '',
      idCard: '',
      phone: '',
      orgId: undefined
    };
    showAddAdminForm.value = false;

    // 重新加载项目管理员列表
    await loadAdminList(tempBindProjectId.value);

    // 刷新主列表
    getList();
  } catch (error: any) {
    proxy?.$modal.msgError(error.message || '绑定失败');
  } finally {
    adminBindLoading.value = false;
  }
};

// 加载项目管理员列表
const loadAdminList = async (projectId: number | string) => {
  adminListLoading.value = true;
  try {
    const res = await getProjectAdmins(projectId);
    if (res.code === 200) {
      // 只显示角色是项目管理员的人员
      const allPersonnel = res.data || [];
      adminList.value = allPersonnel.filter(person => person.roleOnProject === 'PROJECT_ADMIN');
    }
  } catch (error) {
    console.error('加载项目管理员失败', error);
    proxy?.$modal.msgError('加载项目管理员失败');
  } finally {
    adminListLoading.value = false;
  }
};

// 显示新增项目管理员表单
const showAddForm = () => {
  showAddAdminForm.value = true;
  adminBindForm.value = {
    name: '',
    idCard: '',
    phone: '',
    orgId: undefined
  };
};

// 删除管理人员
const deleteAdmin = (row: any) => {
  ElMessageBox.confirm(`确认删除项目管理员"${row.name}"吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await delpersonnel(row.projectPersonnelId);
      proxy?.$modal.msgSuccess('删除成功');
      // 重新加载列表
      if (tempBindProjectId.value) {
        await loadAdminList(tempBindProjectId.value);
      }
    } catch (error: any) {
      proxy?.$modal.msgError(error.message || '删除失败');
    }
  }).catch(() => { });
};

// 取消项目管理员操作
const cancelAdminBind = () => {
  if (showAddAdminForm.value) {
    // 如果在新增表单页面，返回列表页面
    showAddAdminForm.value = false;
    adminBindForm.value = {
      name: '',
      idCard: '',
      phone: '',
      orgId: undefined
    };
  } else {
    // 关闭对话框
    adminBindDialog.visible = false;
    tempBindProjectId.value = null;
    adminList.value = [];
  }
};

// 删除人员
const delperson = (row) => {
  // 确认框
  ElMessageBox.confirm(`确认删除该工作人员?`, {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      delpersonnel(row.projectPersonnelId).then((res) => {
        if (res.code == 200) {
          proxy?.$modal.msgSuccess('删除成功');
          // 获取项目数据

          getProUser(row.projectId).then(res => {
            personnelList.value = res.data
          })
        } else {
          proxy?.$modal.msgError('删除失败');
        }
      });
    })
    .catch(() => { });
};
// 点击取消
let cancelroleselect = () => {
  resetpersonparams();
  personSelectDialog.visible = false;
};
// 取消添加
const delpreviewperson = (index, row) => {
  // 要删除的数据row
  //将字符串personIds.value转为数组
  let newarr = [];
  newarr = personIds.value.split('|');
  // 转为数组对象
  let arr = [];
  // 删除预览列表中的数据
  personnelPreviewList.value.splice(index, 1);
  newarr.forEach((value, index6) => {
    arr = value.split(',');
    if (arr[0] == row.projectPersonnelId && arr[1] == row.roleOnProject) {
      // 删除对应索引数据
      newarr.splice(index6, 1);
    }
  });
  // 转化为字符串赋值给personIds.value
  personIds.value = newarr.join('|');
};

const getpersonlist = () => {
  getEnterpriseUserList(personparams.value).then((Res) => {
    personList.value = Res.rows;
    personlisttotal.value = Res.total;
    personSelectDialog.title = '选择人员';
    personSelectDialog.visible = true;
  });
};
// 获取证书种类字典数据
const getQualificationDict = async () => {
  const res = await listQualificationDict();
  const data = proxy?.handleTree<QualificationDictVO>(res.data, 'id', 'preId');
  if (data) {
    QualificationDictOptions.value = data;
  }
  qualificationDict.value = res.data;
};
/** 获取单位类型标签 */
const getOrgTypeLabel = (type: string) => {
  const dict = sys_dept_type.value.find((dict: any) => dict.value === type);
  return dict ? dict.label : type;
};

/** 搜索组织机构 */
const searchOrg = async () => {
  try {
    orgLoading.value = true;
    const query: DeptQuery = {
      deptType: currentOrgType.value,
      deptName: orgSearchName.value,
      pageNum: 1,
      pageSize: 100
    };

    // 如果是搜索监督站类型，且表单中有cityCode，则添加cityCode参数
    if (currentOrgType.value === 'GOV_QS' && form.value.cityCode) {
      query.cityCode = form.value.cityCode;
    }

    const res = await listDept(query);
    orgList.value = res.data || [];

    orgList.value.forEach((item) => {
      item.selected = item.deptId === dialogSelectedOrgId.value;
    });
  } catch (error) {
    console.error('获取单位列表失败', error);
    proxy?.$modal.msgError('获取单位列表失败');
  } finally {
    orgLoading.value = false;
  }
};

/** 单位类型变更 */
const handleOrgTypeChange = async () => {
  dialogSelectedOrgId.value = '';
  dialogSelectedOrgName.value = '';
  await searchOrg();
};

/** 单位行点击 */
const handleOrgRowClick = (row: any) => {
  dialogSelectedOrgId.value = row.deptId;
  dialogSelectedOrgName.value = row.deptName;

  orgList.value.forEach((item) => {
    item.selected = item.deptId === row.deptId;
  });
};

/** 提交选择单位 */
// 管理员绑定表单
const adminBindFormRef = ref<ElFormInstance>();
const adminBindLoading = ref(false);
const tempBindProjectId = ref<number | string | null>(null);
const adminBindForm = ref({
  name: '',
  idCard: '',
  phone: '',
  orgId: undefined as number | undefined
});

const adminBindRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '身份证号格式不正确', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ]
};

// 管理项目管理员
const handleBindPersonnel = async (row: Prj_projectsVO) => {
  // 重置状态
  showAddAdminForm.value = false;
  adminBindForm.value = {
    name: '',
    idCard: '',
    phone: '',
    orgId: undefined
  };
  adminBindDialog.visible = true;
  tempBindProjectId.value = row.projectId;

  // 加载项目管理员列表
  await loadAdminList(row.projectId);

  // 仅从当前项目的所属监督站和相关单位中提供选项，并标注单位类型
  adminOrgList.value = [];
  try {
    const res = await getPrj_projects(row.projectId);
    const d: any = res.data || {};
    const map = new Map<any, { name: string; type: string }>();
    const add = (id?: any, name?: string, type?: string) => {
      if (id != null && id !== '' && !map.has(id)) map.set(id, { name: name || '', type: type || '' });
    };
    // 具体字段 -> 类型标签
    // add(d.supervisingQsOrgId, d.supervisingQsOrgName, '监督站');
    add(d.clientOrgId, d.clientOrgName, '建设单位');
    add(d.constructionOrgId, d.constructionOrgName, '施工单位');
    add(d.supervisionOrgId, d.supervisionOrgName, '监理单位');
    add(d.designOrgId, d.designOrgName, '设计单位');
    add(d.surveyOrgId, d.surveyOrgName, '勘察单位');
    add(d.installationDismantlingOrgId, d.installationDismantlingOrgName, '安拆单位');
    add(d.maintenanceOrgId, d.maintenanceOrgName, '维保单位');
    // enterpriseList 补充（若有 deptId）
    if (Array.isArray(d.enterpriseList)) {
      const roleMap: Record<string, string> = { '1': '建设单位', '2': '施工单位', '3': '监理单位', '4': '设计单位', '5': '勘察单位' };
      d.enterpriseList.forEach((e: any) => {
        if (e && e.deptId) {
          const tlabel = e.enterpriseRole && roleMap[String(e.enterpriseRole)] ? roleMap[String(e.enterpriseRole)] : '参建单位';
          add(e.deptId, e.enterpriseName, tlabel);
        }
      });
    }
    adminOrgList.value = Array.from(map, ([deptId, obj]) => ({
      deptId,
      deptName: obj.name,
      typeLabel: obj.type,
      displayLabel: (obj.type ? `[${obj.type}] ` : '') + (obj.name || deptId)
    }));
  } catch (error) {
    console.error('加载项目单位失败', error);
  }
};

const submitOrgSelect = () => {
  if (dialogSelectedOrgId.value && currentTargetFieldId.value && currentTargetFieldName.value) {
    (form.value as any)[currentTargetFieldId.value] = dialogSelectedOrgId.value.toString();
    (form.value as any)[currentTargetFieldName.value] = dialogSelectedOrgName.value;
    console.log(
      `表单已更新: ${currentTargetFieldId.value}=${dialogSelectedOrgId.value}, ${currentTargetFieldName.value}=${dialogSelectedOrgName.value}`
    );
  }
  orgSelectDialog.visible = false;
};

/** 取消选择单位 */
const cancelOrgSelect = () => {
  const existingOrgId = (form.value as any)[currentTargetFieldId.value];
  const existingOrgName = (form.value as any)[currentTargetFieldName.value];
  if (existingOrgId) {
    dialogSelectedOrgId.value = existingOrgId;
    dialogSelectedOrgName.value = existingOrgName || '';
  } else {
    dialogSelectedOrgId.value = '';
    dialogSelectedOrgName.value = '';
  }
  orgSelectDialog.visible = false;
};

/** 获取地区树 */
const getArea = async () => {
  try {
    const res = await getAreaTreeGs();
    console.log('获取甘肃省地区树成功');
    areaOptions.value = res.data || [];
  } catch (error) {
    console.error('获取甘肃省地区树失败', error);
    proxy?.$modal.msgError('获取甘肃省地区树失败');
  }
};
// 获取地区树特殊处理后的行政代码查询
const getAreaCode = async (val: number) => {
  try {
    const res = await formatAreaId(val);
    if (res.code === 200) {
      form.value.provinceCode = String(res.data.split('/')[0]);
      form.value.cityCode = String(res.data.split('/')[1]);
      form.value.districtCode = String(res.data.split('/')[2]);
      selectedArea.value[0] = parseInt(res.data.split('/')[0]);
      selectedArea.value[1] = parseInt(res.data.split('/')[1]);
      selectedArea.value[2] = parseInt(res.data.split('/')[2]);
    }
  } catch (error) {
    proxy?.$modal.msgError('获取行政区划代码失败');
  }
};
/** 地区选择变更处理 */
const handleAreaChange = (value: any) => {
  console.log('级联选择器值变更:', value);

  // 清空之前的地区信息
  form.value.provinceCode = undefined;
  form.value.provinceName = undefined;
  form.value.cityCode = undefined;
  form.value.cityName = undefined;
  form.value.districtCode = undefined;
  form.value.districtName = undefined;

  if (!value || value.length === 0) {
    return; // 如果清空了选择，直接返回
  }

  // 按照选择的层级填充表单数据
  // 找到对应的地区节点
  const findNode = (id: number, nodes: any[] = areaOptions.value): any => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children && node.children.length > 0) {
        const found = findNode(id, node.children);
        if (found) return found;
      }
    }
    return null;
  };

  // 省份
  if (value.length >= 1) {
    const provinceId = value[0];
    const province = findNode(provinceId);
    if (province) {
      form.value.provinceCode = String(province.id);
      form.value.provinceName = province.name;
    }
  }

  // 城市
  if (value.length >= 2) {
    const cityId = value[1];
    const city = findNode(cityId);
    if (city) {
      form.value.cityCode = String(city.id);
      form.value.cityName = city.name;
    }
  }

  // 区县
  if (value.length >= 3) {
    const districtId = value[2];
    const district = findNode(districtId);
    if (district) {
      form.value.districtCode = String(district.id);
      form.value.districtName = district.name;
    }
  }

  console.log('表单地区信息已更新:', {
    provinceCode: form.value.provinceCode,
    provinceName: form.value.provinceName,
    cityCode: form.value.cityCode,
    cityName: form.value.cityName,
    districtCode: form.value.districtCode,
    districtName: form.value.districtName
  });
};

/** 地区搜索值变更处理 */
const handleSearchAreaChange = (value: any[]) => {
  console.log('搜索地区值变更:', value);

  // 清空之前的地区搜索参数
  queryParams.value.provinceCode = undefined;
  queryParams.value.cityCode = undefined;
  queryParams.value.districtCode = undefined;

  if (!value || value.length === 0) {
    return; // 如果清空了选择，直接返回
  }

  // 按照选择的层级填充查询参数
  if (value.length >= 1) {
    queryParams.value.provinceCode = String(value[0]);
  }
  if (value.length >= 2) {
    queryParams.value.cityCode = String(value[1]);
  }
  if (value.length >= 3) {
    queryParams.value.districtCode = String(value[2]);
  }

  console.log('搜索参数已更新:', {
    provinceCode: queryParams.value.provinceCode,
    cityCode: queryParams.value.cityCode,
    districtCode: queryParams.value.districtCode
  });
};

/** 单位行样式 */
const orgRowClassName = ({ row }: { row: any }) => {
  return row.deptId === dialogSelectedOrgId.value ? 'selected-row' : '';
};

/** 快速选择单位 */
const quickSelectOrg = (row: any) => {
  handleOrgRowClick(row);
  submitOrgSelect();
};

/** 获取高德地图传过来的地图信息 */
const setLocation = (value: any) => {
  if (!value.lng || !value.lat) return;
  form.value.lola = value.lng + ',' + value.lat;
  form.value.locationDetail = value.address;
  form.value.provinceName = value.addressComponent.province;
  form.value.cityName = value.addressComponent.city;
  form.value.districtName = value.addressComponent.district;
  getAreaCode(value.addressComponent.adcode);
  // this.getAllProvince()
  // this.form1.longitude = value.lng
  // this.form1.latitude = value.lat
  // this.form1.ssqValue[0] = value.addressComponent.province
  // this.form1.ssqValue[1] = value.addressComponent.city
  // this.form1.ssqValue[2] = value.addressComponent.district
  // this.form1.province = value.addressComponent.province
  // this.form1.city = value.addressComponent.city
  // this.form1.area = value.addressComponent.district
  // this.form1.address = value.address

  // this.form1.address = this.$refs.gdmap.searchKeyword
  // this.$refs.gdmap.searchKeyword = value.address
};

/** 项目管理按钮操作 */
const handleProjectManagement = (row: Prj_projectsVO, type) => {
  if (type == 1) {
    proxy?.$tab.openPage(`/prj/prj_hazardous_items/${row.projectId}`);
  } else if (type == 2) {
    proxy?.$tab.openPage(`/prj/prj_hazardous_speical/${row.projectId}`, '', {
      projectName: row.projectName,
      constructionPermitNo: row.constructionPermitNo,
      projectId: row.projectId
    });
  }
};

/** 查看地图 */
const viewMap = (lola: string) => {
  if (lola) {
    window.open(`https://uri.amap.com/marker?position=${lola}`, '_blank');
  }
};

let attendancedialog = ref(false);
let attendancedialogtitle = ref(null);

let signinpersonId = ref(null);
let attendanceList = ref(null);
let attendanceLoading = ref(false);
let AttMonthlist = ref([]);
// 存放当前选中年-月，默认为本月
let currentmonth = ref(null);

// 点击考勤弹窗
let signindetail = (row) => {
  attendancedialog.value = true;
  attendancedialogtitle.value = `考勤详情-${row.name}`;
  // 获取当前日期
  const date = new Date(); // 转为 Date 对象
  // 保存当前人员personId
  signinpersonId.value = row.projectPersonnelId;
  // 本日打卡信息
  getattlist(formatdate(date));
  // 本月
  monthValue.value = date;
  currentmonth.value = formatmonth(date);
  if (signinpersonId.value) {
    getPersonAttMonth(signinpersonId.value, formatmonth(date)).then((Res) => {
      AttMonthlist.value = Res.data;
    });
  }
};

let selTempUser = ref([]);
const selectionWorkerChange = (val) => {
  selTempUser.value = val;
  // 限定五个
  // if(val.length > 5){
  //   proxy?.$modal.msgError('最多只能选择5个');
  //   selTempUser.value = selTempUser.value.slice(0, 5);
  // }
};

let faceMachineDialog = ref(false);
let attSnList = ref([]);
let faceMachine = ref([]);
let tempData = ref(null);
let faceMachineIdList = ref([]);

const handleMachineChange = (val) => {
  if (val) {
    faceMachineIdList.value = attSnList.value
      .map((item) => {
        if (faceMachine.value.some((it) => item.sn == it)) {
          return item.snId;
        }
        return undefined;
      })
      .filter((value) => value !== undefined);
  }
};

const removeUserIssueds = (row) => {
  ElMessageBox.confirm('是否确认清除该用户的人脸数据?', '提示')
    .then(async () => {
      let data = {
        projectId: row.projectId,
        personSnIds: row.mattSnVos.map((item) => item.personSnId),
        sns: row.mattSnVos.map((item) => item.sn).join(','),
        idCard: row.idCard
      };
      let res = await removeUserIssued(data);
      if (res.code == 200) {
        proxy?.$modal.msgSuccess('清空成功');
        handleUserProject(tempRow.value);
      }
    })
    .catch(() => { });
};

const userIssued = async (row, type) => {
  if (type == '0') {
    row = [row];
  }
  tempData.value = row.map((item) => {
    return {
      'id': item.projectPersonnelId,
      'code': item.idCard,
      'content': item.projectId,
      'name': item.name,
      'registerFace': item.url,
      'snList': item.snList ? item.snList.split(',') : null
    };
  });
  let res =
    type == '0'
      ? await getFaceMachine(tempRow.value.projectId, row[0].projectPersonnelId)
      : await listAttSn({
        pageNum: 1,
        pageSize: 1000
      });
  attSnList.value = type == '0' ? res.data : res.rows;
  faceMachineDialog.value = true;
};

let subWait = ref(false);
const submitMachine = () => {
  subWait.value = true;
  tempData.value.forEach((item) => {
    item.snList = faceMachine.value.join(',');
    item.snIds = faceMachineIdList.value.join(',');
  });
  postUserIssued(tempData.value)
    .then((res) => {
      if (res.code == 200) {
        proxy?.$modal.msgSuccess('下发成功');
        cancelMachine();
        handleUserProject(tempRow.value);
      }
    })
    .finally(() => {
      // 这里的代码会在请求完成后始终执行，无论成功或失败
      subWait.value = false;
    });
};
const cancelMachine = () => {
  faceMachineDialog.value = false;
  attSnList.value = [];
  faceMachine.value = [];
};

const formatdate = (date) => {
  // 中国标准日期转化为yyyy-MM-dd格式
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const formattedDate = `${year}-${month}-${day}`;
  return formattedDate;
};
const formatmonth = (date) => {
  // 中国标准日期转化为yyyy-MM-dd格式
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const formattedDate = `${year}-${month}`;
  return formattedDate;
};

let selectedDate = ref(null);

watch(
  () => selectedDate.value,
  (newVal) => {
    if (newVal) {
      getattlist(formatdate(newVal));
      // 赋值
      monthValue.value = newVal;

      currentmonth.value = formatmonth(newVal);
    }
  }
);
let calendarLoading = ref(false);
// 监听月份变化
watch(
  () => currentmonth.value,
  (newVal, oldval) => {
    if (oldval == null) {
      return;
    }
    if (newVal) {
      if (signinpersonId.value) {
        calendarLoading.value = true;
        getPersonAttMonth(signinpersonId.value, newVal).then(
          (Res) => {
            calendarLoading.value = false;
            AttMonthlist.value = Res.data;
          },
          (error) => {
            calendarLoading.value = false;
          }
        );
      }
    }
  }
);

let attenRules = ref(null);
const getattlist = (dayStr) => {
  attendanceLoading.value = true;
  if (signinpersonId.value) {
    getPersonAtt(signinpersonId.value, dayStr).then(
      (Res) => {
        attendanceLoading.value = false;
        attendanceList.value = Res.data;

        // 考勤规则
        if (attendanceList.value.length > 0 && attendanceList.value[0].mattRules.length > 0) {
          attenRules.value = JSON.parse(attendanceList.value[0].mattRules[0].checkTime);
        }
      },
      (error) => {
        attendanceLoading.value = false;
      }
    );
  }
};

// 日期大小比较
const comparedate = (startTime) => {
  //当前时间
  var currentDate = new Date();

  // 加0 ，否则比较不一致当天的会出错 2025-05-14 2025-5-14转为时间戳不相等
  const currentDateStr = formatdate(currentDate);
  if (new Date(currentDateStr).getTime() < new Date(startTime).getTime()) {
    // console.log('开始时间不能大于当前时间');
    return false;
  } else {
    return true;
  }
};

const comparedate2 = (startTime) => {
  //开工日期detailForm.startDate
  // 判断有没有实际开工日期，没有就用计划施工日期
  let currentDateStr = null;
  if (tempRow.value.actualStartDate) {
    currentDateStr = tempRow.value.actualStartDate.match(/\d{4}-\d{2}-\d{2}/)?.[0] || tempRow.value.startDate;
    if (new Date(currentDateStr).getTime() >= new Date(startTime).getTime()) {
      return false;
    } else {
      return true;
    }
  } else {
    currentDateStr = tempRow.value.startDate.match(/\d{4}-\d{2}-\d{2}/)?.[0] || tempRow.value.startDate;
    if (new Date(currentDateStr).getTime() >= new Date(startTime).getTime()) {
      return false;
    } else {
      return true;
    }
  }
};

let monthValue = ref(null);
const changeMonth = () => {
  selectedDate.value = new Date(monthValue.value);
  currentmonth.value = formatmonth(monthValue.value);
};
// 点击关闭考勤
let closeatt = () => {
  // 清除时间和月份信息
  attendanceList.value = null;
  signinpersonId.value = null;
  AttMonthlist.value = null;
  selectedDate.value = new Date();
  currentmonth.value = null;
  monthValue.value = null;
  // 关闭弹窗
  attendancedialog.value = false;
};
onMounted(() => {
  getList();
  getArea();
  // comparedate2('2025-06-19');
});
</script>

<style scoped>
.is-selected {
  color: #1989fa;
}

.selected-row {
  background-color: #f0f9ff !important;
  font-weight: bold;
}

.el-table :deep(.selected-row) td {
  background-color: #f0f9ff !important;
}

.latLong {
  width: 100%;
}

.basicinfo {
  display: flex;
  align-items: center;

  .ercode {
    text-align: center;
    margin-right: 40px;
  }

  .basicinfo-left {
    flex: 1;
  }
}

.basicinfotitle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .gang {
    height: 15px;
    width: 3px;
    background-color: #409eff;
    border-radius: 6px;
    margin-right: 5px;
  }

  .basicinfoname {
    font-weight: 600;
  }
}

/* 固定标签宽度并禁止换行 */
.el-descriptions-item__label {
  width: 120px !important;
  /* 根据需求调整 */
  white-space: nowrap;
  /* 禁止标签换行 */
  text-overflow: ellipsis;
  /* 溢出显示省略号 */
  overflow: hidden;
}

/* 内容区域允许换行 */
.el-descriptions-item__content {
  word-break: break-word;
  /* 长文本自动换行 */
}

.el-descriptions-item__label {
  flex-shrink: 0;
  /* 禁止标签收缩 */
}

/* ::v-deep(.el-upload-list__item) {
  border: none !important;
} */
.prodetail {
  border-bottom: 1px solid rgba(64, 158, 255, 0);
}

.prodetail:hover {
  color: #409eff;
  border-bottom: 1px solid rgb(64, 158, 255);
}

.normalefficacy {
  background-color: rgb(64, 158, 255);
  margin-right: 6px;
}

.loseefficacy {
  background-color: rgb(255, 0, 0);
  margin-right: 6px;
}

.detailinfo {
  display: flex;
  align-items: center;
}

.absence {
  background-color: rgb(248, 146, 3);
  margin-right: 6px;
}

.dian {
  height: 10px;
  width: 10px;
  border-radius: 50%;
}

.Calendarselect {
  position: absolute;
  right: 200px;
  top: 11px;
}

::v-deep .el-calendar__body {
  height: 73vh !important;
}

::v-deep .el-card__body {
  max-height: 90%;
  overflow: auto;
}

:deep(.el-dialog__body) {
  max-height: 80% !important;
  overflow: hidden !important;
}
</style>
