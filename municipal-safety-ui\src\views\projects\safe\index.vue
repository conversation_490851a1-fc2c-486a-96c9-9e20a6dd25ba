<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="项目名称" prop="projectId">
              <el-select v-model="queryParams.projectId" filterable placeholder="请选择项目" style="width: 240px" clearable
                @change="handleQuery">
                <el-option v-for="item in projectSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="塔机现场编号" prop="projectCraneNum" label-width="100px">
              <el-input v-model="queryParams.projectCraneNum" placeholder="请输入塔机现场编号" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="执行日期" prop="executioinDate">
              <el-date-picker v-model="executioinDate" type="datetimerange" range-separator="-" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="YYYY-MM-DD" date-format="YYYY-MM-DD" time-format="YYYY-MM-DD"
                format="YYYY-MM-DD" clearable @change="handleDateChange" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" filterable placeholder="请选择状态" clearable @change="handleQuery">
                <el-option v-for="dict in safe_task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header v-if="false">
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['safeTask:safeTask:add']">新增</el-button>
          </el-col> -->
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['safeTask:safeTask:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['safeTask:safeTask:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['safeTask:safeTask:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="safeTaskList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="项目名称" align="center" prop="projectName" width="200px">
          <template #default="scope">
            <div class="prodetail" style="cursor: pointer" @click="handleViewDetail(scope.row)">{{ scope.row.projectName
            }}</div>
          </template>
        </el-table-column>
        <el-table-column label="项目地址" align="center" prop="projectAddress" width="240px" />
        <el-table-column label="安拆单位名称" align="center" prop="installationUnitName" width="180px" />
        <el-table-column label="塔机现场编号" align="center" prop="projectCraneNum" />
        <el-table-column label="塔机类型" align="center" prop="craneType">
          <template #default="scope">
            <dict-tag :options="safe_crane_type" :value="scope.row.craneType" />
          </template>
        </el-table-column>
        <el-table-column label="顶升降节作业类型" align="center" prop="jackingType">
          <template #default="scope">
            <dict-tag :options="safe_jacking_type" :value="scope.row.jackingType" />
          </template>
        </el-table-column>
        <el-table-column label="执行日期" align="center" prop="executionDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.executionDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="safe_task_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip v-if="scope.row.status == 'CONDITION_AUDIT'" content="审核" placement="top">
              <el-button link type="primary" icon="CircleCheck" @click="handleToExamine(scope.row)"
                v-hasPermi="['safeTask:safeTask:auditTask']"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
                v-hasPermi="['safeTask:safeTask:detail']"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['safeTask:safeTask:edit']"></el-button>
            </el-tooltip> -->
            <el-tooltip v-if="scope.row.status == 'NO_UP'" content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['safeTask:safeTask:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改【项目管理】安拆任务对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1200px" append-to-body>
      <el-form ref="safeTaskFormRef" :model="form" :rules="rules" label-width="80px">
        <el-divider content-position="left">基本信息</el-divider>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="项目名称" prop="projectName" label-width="140px">
              <el-input v-model="form.projectName" placeholder="请选择项目" readonly>
                <template #append>
                  <el-button-group>
                    <el-button @click="selectProjectChange">选择</el-button>
                  </el-button-group>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="项目地址" prop="projectAddress" label-width="140px">
              <el-input v-model="form.projectAddress" placeholder="请输入项目地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="项目经度" prop="projectLongitude" label-width="140px">
              <el-input v-model="form.projectLongitude" placeholder="请输入项目经度" />
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="项目纬度" prop="projectLatitude" label-width="140px">
              <el-input v-model="form.projectLatitude" placeholder="请输入项目纬度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="塔机现场编号" prop="projectCraneNum" label-width="140px">
              <el-input v-model="form.projectCraneNum" placeholder="请输入塔机现场编号" />
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="塔机类型" prop="craneType" label-width="140px">
              <el-select v-model="form.craneType" placeholder="请选择塔机类型">
                <el-option v-for="dict in safe_crane_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="塔机规格型号" prop="craneModel" label-width="140px">
              <el-input v-model="form.craneModel" placeholder="请输入塔机规格型号" />
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="塔机出厂编号" prop="craneSn" label-width="140px">
              <el-input v-model="form.craneSn" placeholder="请输入塔机出厂编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="塔机出厂日期" prop="craneProductionDate" label-width="140px">
              <el-date-picker style="width: 100%;" clearable v-model="form.craneProductionDate" type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择塔机出厂日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="产权单位名称" prop="propertyCompanyName" label-width="140px">
              <el-input v-model="form.propertyCompanyName" placeholder="请输入产权单位名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="塔机生产厂商名称" prop="factoryName" label-width="140px">
              <el-input v-model="form.factoryName" placeholder="请输入塔机生产厂商名称" />
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="顶升降节作业类型" prop="jackingType" label-width="140px">
              <el-select v-model="form.jackingType" placeholder="请选择顶升降节作业类型">
                <el-option v-for="dict in safe_jacking_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="执行日期" prop="executionDate" label-width="140px">
              <el-date-picker style="width: 100%;" clearable v-model="form.executionDate" type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择执行日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="升/降节数" prop="sectionNum" label-width="140px">
              <el-input v-model="form.sectionNum" placeholder="请输入升/降节数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="加降节前塔机高度" prop="initialCraneHeight" label-width="140px">
              <el-input v-model="form.initialCraneHeight" placeholder="请输入加降节前塔机高度" />
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="加降节后塔机高度" prop="modifiedCraneHeight" label-width="140px">
              <el-input v-model="form.modifiedCraneHeight" placeholder="请输入加降节后塔机高度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="11">
            <el-form-item label="安拆单位名称" prop="installationUnitName" label-width="140px">
              <el-input v-model="form.installationUnitName" placeholder="请输入安拆单位名称" />
            </el-form-item>
          </el-col>
          <el-col :span="13">
            <el-form-item label="安拆单位资质地址" prop="installationUnitQualification" label-width="140px">
              <el-input v-model="form.installationUnitQualification" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="安全生产许可证地址" prop="safetyProductionPermit" label-width="140px">
              <el-input v-model="form.safetyProductionPermit" placeholder="请输入安全生产许可证地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">项目人员</el-divider>
        <el-row>
          <el-col :span="24">
            <el-row class="mb8" style="margin: 10px 0;">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" size="small" @click="handleAddProjectUser"
                  v-hasPermi="['safeTask:safeTask:add']">添加项目人员</el-button>
              </el-col>
            </el-row>
            <el-table stripe :data="form.users" border>
              <el-table-column prop="userName" label="姓名" />
              <el-table-column prop="idCard" label="身份证号" />
              <el-table-column prop="mobile" label="手机号" />
              <el-table-column prop="positionType" label="岗位类型">
                <template #default="scope">
                  <dict-tag :options="safe_position_type" :value="scope.row.positionType" />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="100px">
                <template #default="scope">
                  <el-button type="text" @click="handleDeleteProjectUser(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-divider content-position="left" style="margin-top: 30px;">安拆人员</el-divider>
        <el-row>
          <el-col :span="24">
            <el-row class="mb8" style="margin: 10px 0;">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" size="small" @click="handleAddInstallation"
                  v-hasPermi="['safeTask:safeTask:add']">添加安拆人员</el-button>
              </el-col>
            </el-row>
            <el-table stripe :data="form.installations" border>
              <el-table-column prop="userName" label="姓名" />
              <el-table-column prop="userPositionName" label="岗位名称" />
              <el-table-column prop="face" label="人脸照片" />
              <el-table-column prop="certificate" label="资质证书" />
              <el-table-column label="操作" align="center" width="100px">
                <template #default="scope">
                  <el-button type="text" @click="handleDeleteInstallation(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-divider content-position="left" style="margin-top: 30px;">旁站人员</el-divider>
        <el-row>
          <el-col :span="24">
            <el-row class="mb8" style="margin: 10px 0;">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" size="small" @click="handleAddSafeSupervision"
                  v-hasPermi="['safeTask:safeTask:add']">添加旁站人员</el-button>
              </el-col>
            </el-row>
            <el-table stripe :data="form.safeSupervisions" border>
              <el-table-column prop="userName" label="姓名" />
              <el-table-column prop="userPositionName" label="岗位名称" />
              <el-table-column prop="face" label="人脸照片" />
              <el-table-column prop="certificate" label="资质证书" />
              <el-table-column label="操作" align="center" width="100px">
                <template #default="scope">
                  <el-button type="text" @click="handleDeleteSafeSupervision(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入选择项目列表的组件 -->
    <Project :isShowModel="isShowModel" @update:isShowModel="isShowModelChange"
      @selectionProjectData="handleSelectionProject" />
    <!-- 添加项目人员的对话框 -->
    <el-dialog :title="projectUserDialog.title" v-model="projectUserDialog.visible" width="500px" append-to-body
      @open="resetPersonForm('projectUser')">
      <el-form ref="projectUserFormRef" :model="projectUserForm" :rules="projectUserRules" label-width="80px">
        <el-form-item label="姓名" prop="userName">
          <el-input v-model="projectUserForm.userName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="projectUserForm.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="projectUserForm.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="岗位类型" prop="positionType">
          <el-select v-model="projectUserForm.positionType" placeholder="请选择岗位类型">
            <el-option v-for="dict in safe_position_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmProjPerson">确 定</el-button>
          <el-button @click="cancelProjPerson">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加安拆人员的对话框 -->
    <el-dialog :title="projectInstallationDialog.title" v-model="projectInstallationDialog.visible" width="500px"
      append-to-body @open="resetPersonForm('projectInstallation')">
      <el-form ref="installationFormRef" :model="installationForm" :rules="installationRules" label-width="80px">
        <el-form-item label="姓名" prop="userName">
          <el-input v-model="installationForm.userName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="岗位名称" prop="userPositionName">
          <el-input v-model="installationForm.userPositionName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="人脸照片" prop="face">
          <el-input v-model="installationForm.face" placeholder="请输入人脸照片" />
        </el-form-item>
        <el-form-item label="资质证书" prop="certificate">
          <el-input v-model="installationForm.certificate" placeholder="请输入资质证书" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmInstallation">确 定</el-button>
          <el-button @click="cancelInstallation">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加旁站人员的对话框 -->
    <el-dialog :title="projectSupervisionDialog.title" v-model="projectSupervisionDialog.visible" width="500px"
      append-to-body @open="resetPersonForm('projectSupervision')">
      <el-form ref="supervisionFormRef" :model="supervisionForm" :rules="supervisionRules" label-width="80px">
        <el-form-item label="姓名" prop="userName">
          <el-input v-model="supervisionForm.userName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="岗位名称" prop="userPositionName">
          <el-input v-model="supervisionForm.userPositionName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="人脸照片" prop="face">
          <el-input v-model="supervisionForm.face" placeholder="请输入人脸照片" />
        </el-form-item>
        <el-form-item label="资质证书" prop="certificate">
          <el-input v-model="supervisionForm.certificate" placeholder="请输入资质证书" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmSupervision">确 定</el-button>
          <el-button @click="cancelSupervision">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 审核的弹框 -->
    <el-dialog :title="examineDialog.title" v-model="examineDialog.visible" width="600px" append-to-body>
      <el-form ref="examineFormRef" :model="examineForm" :rules="examineRules" label-width="80px">
        <el-form-item label="审核状态" prop="status">
          <el-select v-model="examineForm.status" placeholder="请选择审核状态">
            <el-option v-for="dict in examineStatusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditMsg">
          <el-input :autosize="{ minRows: 5, maxRows: 7 }" type="textarea" v-model="examineForm.auditMsg"
            placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmExamine">确 定</el-button>
          <el-button @click="examineDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 项目详情弹窗 -->
    <el-dialog title="项目详情" v-model="detailDialog.visible" width="90vw" style="height: 90vh; overflow-y: auto"
      append-to-body>
      <el-card style="margin-bottom: 10px">
        <div class="basicinfo">
          <div class="basicinfo-left">
            <div class="basicinfotitle">
              <div class="gang"></div>
              <div class="basicinfoname">{{ detailForm.projectName }}</div>
            </div>
            <el-descriptions label-width="140px" :column="3" border>
              <el-descriptions-item label="项目编码">{{ detailForm.projectCode }}</el-descriptions-item>
              <el-descriptions-item label="工程概况" :span="2">{{ detailForm.projectOverview }}</el-descriptions-item>
              <el-descriptions-item label="施工许可证编号">{{ detailForm.constructionPermitNo }}</el-descriptions-item>
              <el-descriptions-item label="占地面积" v-if="detailForm.siteArea">{{ detailForm.siteArea }}
                平方米
              </el-descriptions-item>
              <el-descriptions-item label="预算投资总额" v-if="detailForm.budgetTotal">{{ detailForm.budgetTotal }}
                万元
              </el-descriptions-item>
              <el-descriptions-item label="项目位置地图">
                {{ detailForm.lola }}
                <el-button v-if="detailForm.lola" type="primary" link @click="viewMap(detailForm.lola)">查看</el-button>
              </el-descriptions-item>
              <el-descriptions-item label="所在地区">
                {{ detailForm.provinceName || '-' }} {{ detailForm.cityName ? '/ ' + detailForm.cityName : '' }}
                {{ detailForm.districtName ? '/ ' + detailForm.districtName : '' }}
              </el-descriptions-item>
              <el-descriptions-item label="详细地址">{{ detailForm.locationDetail || '-' }}</el-descriptions-item>
              <el-descriptions-item label="项目状态">
                <dict-tag :options="prj_projects_status" :value="detailForm.status" />
              </el-descriptions-item>
              <el-descriptions-item label="计划工期">
                {{ parseTime(detailForm.startDate, '{y}-{m}-{d}') || '-' }} 至
                {{ parseTime(detailForm.plannedEndDate, '{y}-{m}-{d}') || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="实际工期" v-if="detailForm.actualStartDate">
                {{ parseTime(detailForm.actualStartDate, '{y}-{m}-{d}') || '-' }} 至
                {{ parseTime(detailForm.actualEndDate, '{y}-{m}-{d}') || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="施工许可证扫描件" v-if="detailForm.constructionPermitDocId">
                <div @click="viewfile(detailForm.constructionPermitDocId)" style="cursor: pointer; color: #409eff">点击查看
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="所属监督站">{{ detailForm.supervisingQsOrgName || '-' }} </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>
      <!-- 参建单位 -->
      <el-card style="margin-bottom: 10px">
        <div slot="header">
          <div class="punits">
            <div class="basicinfotitle">
              <div class="gang"></div>
              <div class="basicinfoname">参建单位</div>
            </div>
          </div>
        </div>
        <!-- 单位信息 -->
        <el-table stripe :data="detailForm.enterpriseList" border style="width: 100%">
          <el-table-column prop="enterpriseType" label="企业类型">
            <template #default="scope">
              <dict-tag :options="enterprise_type" :value="scope.row.enterpriseType" />
            </template>
          </el-table-column>
          <el-table-column prop="enterpriseName" label="单位名称" min-width="180" />
          <el-table-column prop="unifiedSocialCreditCode" label="统一社会信用代码" />
          <el-table-column prop="legalRepresentative" label="法人代表" />
          <el-table-column prop="officePhone" label="办公电话" min-width="120" />
        </el-table>
      </el-card>
      <!-- 相关人员 -->
      <el-card style="margin-bottom: 10px" v-if="detailForm.personnelList && detailForm.personnelList.length > 0">
        <div slot="header">
          <div class="punits">
            <div class="basicinfotitle">
              <div class="gang"></div>
              <div class="basicinfoname">相关人员</div>
            </div>
          </div>
        </div>
        <!-- 人员信息 -->
        <el-table stripe :data="detailForm.personnelList" border style="width: 100%">
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="idCard" label="证件号码" width="180" />
          <el-table-column prop="roleOnProject" label="角色类型" width="180">
            <template #default="scope">
              <dict-tag :options="personnel_position" :value="scope.row.roleOnProject" />
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号码" width="120" />
          <el-table-column prop="enterpriseName" label="所在单位" />
          <el-table-column label="性别" align="center" prop="gender" width="80px">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="文化程度" align="center" prop="education" width="120px">
            <template #default="scope">
              <dict-tag :options="educational_level_code" :value="scope.row.education" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-dialog>
    <!-- 安拆任务的详情 -->
    <SafeDetail :openTaskId="openTaskId" @emitSafeDetail="emitSafeDetail" />
  </div>
</template>

<script setup name="SafeTask" lang="ts">
import { listSafeTaskOut, getSafeTask, delSafeTask, addSafeTask, updateSafeTask, getProjectInfo, get_prj_search_data, auditSafeTask } from '@/api/projects/safe/index';
import { SafeTaskVO, SafeTaskQuery, SafeTaskForm, ProjectUserForm, ProjectInstallationForm, ProjectSupervisionForm } from '@/api/projects/safe/types';
import { Prj_projectsVO, Prj_projectsForm } from '@/api/projects/prj_projects/types';
import { getPrj_projects } from '@/api/projects/prj_projects';
import { listByIds } from '@/api/system/oss';
import Project from '@/components/Project/index.vue';
import SafeDetail from './components/safeDetail.vue';
import _ from 'lodash';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { prj_projects_status, safe_crane_type, safe_jacking_type, safe_position_type, safe_task_status, enterprise_type, personnel_position, sys_user_sex, educational_level_code } = toRefs<any>(
  proxy?.useDict('prj_projects_status', 'safe_crane_type', 'safe_jacking_type', 'safe_position_type', 'safe_task_status', 'enterprise_type', 'personnel_position', 'sys_user_sex', 'educational_level_code')
);

const openTaskId = ref<string | number>();
const safeTaskList = ref<SafeTaskVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const safeTaskFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: SafeTaskForm = {
  installations: [],   //安拆人员
  users: [],       //项目人员
  safeSupervisions: [],  //旁站人员
  projectId: undefined,
  projectName: undefined,
  projectAddress: undefined,
  projectLongitude: undefined,
  projectLatitude: undefined,
  projectCraneNum: undefined,
  craneType: undefined,
  craneModel: undefined,
  craneSn: undefined,
  craneProductionDate: undefined,
  propertyCompanyName: undefined,
  factoryName: undefined,
  jackingType: undefined,
  executionDate: undefined,
  sectionNum: undefined,
  modifiedCraneHeight: undefined,
  initialCraneHeight: undefined,
  installationUnitName: undefined,
  installationUnitQualification: undefined,
  safetyProductionPermit: undefined
}
const data = reactive<PageData<SafeTaskForm, SafeTaskQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    projectCraneNum: undefined,
    executioinDateStart: undefined,
    executioinDateEnd: undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
    projectName: [
      { required: true, message: "项目名称不能为空", trigger: "change" }
    ],
    projectCraneNum: [
      { required: true, message: "塔机现场编号不能为空", trigger: "blur" }
    ],
    craneType: [
      { required: true, message: "塔机类型不能为空", trigger: "change" }],
    craneModel: [
      { required: true, message: "塔机规格型号不能为空", trigger: "blur" }
    ],
    craneSn: [
      { required: true, message: "塔机出厂编号不能为空", trigger: "blur" }
    ],
    craneProductionDate: [
      { required: true, message: "塔机出厂日期不能为空", trigger: "blur" }
    ],
    propertyCompanyName: [
      { required: true, message: "产权单位名称不能为空", trigger: "blur" }
    ],
    factoryName: [
      { required: true, message: "塔机生产厂商名称不能为空", trigger: "blur" }
    ],
    jackingType: [
      { required: true, message: "顶升降节作业类型不能为空", trigger: "change" }
    ],
    executionDate: [
      { required: true, message: "执行日期不能为空", trigger: "blur" }
    ],
    installationUnitName: [
      { required: true, message: "安拆单位名称不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 控制项目对话框的显示与隐藏
const isShowModel = ref(false);

// 搜索的执行日期范围值
const executioinDate = ref([]);
// 添加项目人员的表单
const projectUserFormRef = ref<ElFormInstance>();
const projectUserForm = ref<ProjectUserForm>({
  mobile: undefined,
  userName: undefined,
  idCard: undefined,
  positionType: undefined,
});
const projectUserRules = reactive<any>({
  userName: [
    { required: true, message: "姓名不能为空", trigger: "blur" }
  ],
  mobile: [
    { required: true, message: "手机号不能为空", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: Function) => {
        const reg = /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/;
        if (!reg.test(value)) {
          callback(new Error("请输入正确的手机号"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  idCard: [
    { required: true, message: "身份证号不能为空", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: Function) => {
        const reg = /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/;
        if (!reg.test(value)) {
          callback(new Error("请输入正确的身份证号"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  positionType: [
    { required: true, message: "岗位类型不能为空", trigger: "change" }
  ]
});
// 添加安拆人员的表单
const installationFormRef = ref<ElFormInstance>();
const installationForm = ref<ProjectInstallationForm>({
  userName: undefined,
  userPositionName: undefined,
  face: undefined,
  certificate: undefined,
});
const installationRules = reactive<any>({
  userName: [
    { required: true, message: "姓名不能为空", trigger: "blur" }
  ],
  userPositionName: [
    { required: true, message: "岗位名称不能为空", trigger: "blur" }
  ],
  face: [
    { required: true, message: "人脸照片不能为空", trigger: "blur" }
  ],
  certificate: [
    { required: true, message: "资质证书不能为空", trigger: "blur" }
  ]
});
// 添加旁站人员的表单
const supervisionFormRef = ref<ElFormInstance>();
const supervisionForm = ref<ProjectSupervisionForm>({
  userName: undefined,
  userPositionName: undefined,
  face: undefined,
  certificate: undefined,
});
const supervisionRules = reactive<any>({
  userName: [
    { required: true, message: "姓名不能为空", trigger: "blur" }
  ],
  userPositionName: [
    { required: true, message: "岗位名称不能为空", trigger: "blur" }
  ],
  face: [
    { required: true, message: "人脸照片不能为空", trigger: "blur" }
  ],
  certificate: [
    { required: true, message: "资质证书不能为空", trigger: "blur" }
  ]
});
// 控制添加项目人员对话框显示
const projectUserDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
// 控制添加安拆人员对话框显示
const projectInstallationDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
// 控制添加旁站人员对话框显示
const projectSupervisionDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const projectSelectData = ref([])

// 控制审核的对话框显示
const examineDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
// 审核状态的options参数
const examineStatusOptions = ref([
  { label: "通过", value: 1 },
  { label: "驳回", value: 2 },
])
// 审核弹框元素的ref
const examineFormRef = ref<ElFormInstance>();
// 审核时提交的数据
const examineForm = ref<{ openTaskId: string | number; status: number; auditMsg: string }>({
  openTaskId: undefined,
  status: undefined,
  auditMsg: undefined,
});
const examineRules = reactive<any>({
  status: [
    { required: true, message: "审核状态不能为空", trigger: "change" }
  ]
})

// 项目详情弹窗的所有变量定义
const detailDialog = reactive<DialogOption>({
  visible: false
});
const detailForm = reactive<Prj_projectsForm>({} as Prj_projectsForm);
let fileurl = ref('');

const viewfile = (id) => {
  listByIds(id).then((res) => {
    fileurl.value = res.data[0].url;
    window.open(fileurl.value);
  });
};
/** 查看地图 */
const viewMap = (lola: string) => {
  if (lola) {
    window.open(`https://uri.amap.com/marker?position=${lola}`, '_blank');
  }
};
/** 查看项目详情的事件方法 */
const handleViewDetail = async (row: Prj_projectsVO) => {
  try {
    const res = await getPrj_projects(row.projectId);
    Object.assign(detailForm, res.data);
    detailDialog.visible = true;
  } catch (error) {
    console.error('获取项目详情失败', error);
    proxy?.$modal.msgError('获取项目详情失败');
  } finally { }
};

// 审核弹框确定按钮事件
const confirmExamine = async () => {
  examineFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const res = await auditSafeTask(examineForm.value);
      if (res.code == 200) {
        proxy?.$modal.msgSuccess("提交成功");
        examineDialog.visible = false;
        getList();
      }
    }
  })
}
// 重置审核弹框表单数据
const resetExamineForm = () => {
  examineForm.value.status = undefined;
  examineForm.value.auditMsg = undefined;
  examineFormRef.value?.resetFields();
}
// 执行日期搜索
const handleDateChange = (value: any) => {
  queryParams.value.executioinDateStart = value ? value[0] : undefined;
  queryParams.value.executioinDateEnd = value ? value[1] : undefined;
  handleQuery();
};
//查询项目选择框数据
const getProjectSelectData = async () => {
  const { data } = await get_prj_search_data();
  projectSelectData.value = data
}
// 重置项目人员、安拆人员、旁站人员表单数据
const resetPersonForm = (val: string) => {
  if (val === 'projectUser') {
    projectUserFormRef.value?.resetFields();
  } else if (val === 'projectInstallation') {
    installationFormRef.value?.resetFields();
  } else if (val === 'projectSupervision') {
    supervisionFormRef.value?.resetFields();
  }
}
// 添加项目人员确实事件
const confirmProjPerson = () => {
  projectUserFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const cloneVal = _.cloneDeep(projectUserForm.value);
      form.value.users.push(cloneVal);
      proxy?.$modal.msgSuccess("操作成功");
      projectUserDialog.visible = false;
    }
  });
}
// 添加项目人员取消事件
const cancelProjPerson = () => {
  resetPersonForm('projectUser');
  projectUserDialog.visible = false;
}
// 添加安拆人员确定事件
const confirmInstallation = () => {
  installationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const cloneVal = _.cloneDeep(installationForm.value);
      form.value.installations.push(cloneVal);
      proxy?.$modal.msgSuccess("操作成功");
      projectInstallationDialog.visible = false;
    }
  });
}
// 添加安拆人员取消事件
const cancelInstallation = () => {
  resetPersonForm('projectInstallation');
  projectInstallationDialog.visible = false;
}
// 添加旁站人员确定事件
const confirmSupervision = () => {
  supervisionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const cloneVal = _.cloneDeep(supervisionForm.value);
      form.value.safeSupervisions.push(cloneVal);
      proxy?.$modal.msgSuccess("操作成功");
      projectSupervisionDialog.visible = false;
    }
  });
}
// 添加旁站人员取消事件
const cancelSupervision = () => {
  resetPersonForm('projectSupervision');
  projectSupervisionDialog.visible = false;
}
/** 查询【项目管理】安拆任务列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSafeTaskOut(queryParams.value);
  safeTaskList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  form.value.users = [];
  form.value.installations = [];
  form.value.safeSupervisions = [];
  safeTaskFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  executioinDate.value = [];
  queryParams.value.executioinDateStart = undefined;
  queryParams.value.executioinDateEnd = undefined;
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: SafeTaskVO[]) => {
  ids.value = selection.map(item => item.openTaskId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
// 添加项目人员操作按钮
const handleAddProjectUser = () => {
  projectUserDialog.visible = true;
  projectUserDialog.title = "添加项目人员";
}
// 删除项目人员操作按钮
const handleDeleteProjectUser = (row: ProjectUserForm) => {
  proxy?.$modal.confirm("确定删除该项目人员吗？").then(() => {
    form.value.users = form.value.users.filter(item => item.idCard !== row.idCard);
    proxy?.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
// 添加安拆人员操作按钮
const handleAddInstallation = () => {
  projectInstallationDialog.visible = true;
  projectInstallationDialog.title = "添加安拆人员";
}
// 删除安拆人员操作按钮
const handleDeleteInstallation = (row: ProjectInstallationForm) => {
  proxy?.$modal.confirm("确定删除该安拆人员吗？").then(() => {
    form.value.installations = form.value.installations.filter(item => item.face !== row.face);
    proxy?.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
// 删除旁站人员操作按钮
const handleDeleteSafeSupervision = (row: ProjectSupervisionForm) => {
  proxy?.$modal.confirm("确定删除该旁站人员吗？").then(() => {
    form.value.safeSupervisions = form.value.safeSupervisions.filter(item => item.face !== row.face);
    proxy?.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
// 添加旁站人员操作按钮
const handleAddSafeSupervision = () => {
  projectSupervisionDialog.visible = true;
  projectSupervisionDialog.title = "添加旁站人员";
}
/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加安拆任务";
}
// 审核按钮操作
const handleToExamine = async (row?: SafeTaskVO) => {
  resetExamineForm();
  examineForm.value.openTaskId = row?.openTaskId;
  examineDialog.title = "审核";
  examineDialog.visible = true;
}
// 安拆任务详情按钮操作
const handleDetail = (row: SafeTaskVO) => {
  openTaskId.value = row.openTaskId;
}
/** 修改按钮操作 */
const handleUpdate = async (row?: SafeTaskVO) => {
  reset();
  const _openTaskId = row?.openTaskId || ids.value[0]
  const res = await getSafeTask(_openTaskId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改安拆任务";
}

/** 提交按钮 */
const submitForm = () => {
  safeTaskFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.openTaskId) {
        await updateSafeTask(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addSafeTask(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: SafeTaskVO) => {
  const _openTaskIds = row?.openTaskId || ids.value;
  await proxy?.$modal.confirm('是否确认删除安拆任务编号为"' + _openTaskIds + '"的数据项？').finally(() => loading.value = false);
  await delSafeTask(_openTaskIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
// const handleExport = () => {
//   proxy?.download('safeTask/safeTask/export', {
//     ...queryParams.value
//   }, `safeTask_${new Date().getTime()}.xlsx`)
// }

// 选择项目按钮事件
const selectProjectChange = () => {
  isShowModel.value = true;
};
// 项目列表中选中项目以后的自定义事件
const handleSelectionProject = async (data: { projectId: string; projectName: string }) => {
  form.value.projectId = data.projectId;
  form.value.projectName = data.projectName;
  isShowModel.value = false;
  const res = await getProjectInfo(data.projectId);
  if (res.code == 200) {
    form.value.projectAddress = res.data.projectAddress ? res.data.projectAddress : form.value.projectAddress;
    form.value.projectLongitude = res.data.projectLongitude ? res.data.projectLongitude : form.value.projectLongitude;
    form.value.projectLatitude = res.data.projectLatitude ? res.data.projectLatitude : form.value.projectLatitude;
    form.value.projectCraneNum = res.data.projectCraneNum ? res.data.projectCraneNum : form.value.projectCraneNum;
    form.value.craneType = res.data.craneType ? res.data.craneType : form.value.craneType;
    form.value.craneModel = res.data.craneModel ? res.data.craneModel : form.value.craneModel;
    form.value.craneSn = res.data.craneSn ? res.data.craneSn : form.value.craneSn;
    form.value.craneProductionDate = res.data.craneProductionDate ? res.data.craneProductionDate : form.value.craneProductionDate;
    form.value.propertyCompanyName = res.data.propertyCompanyName ? res.data.propertyCompanyName : form.value.propertyCompanyName;
    form.value.factoryName = res.data.factoryName ? res.data.factoryName : form.value.factoryName;
    form.value.jackingType = res.data.jackingType ? res.data.jackingType : form.value.jackingType;
    form.value.executionDate = res.data.executionDate ? res.data.executionDate : form.value.executionDate;
    form.value.sectionNum = res.data.sectionNum ? res.data.sectionNum : form.value.sectionNum;
    form.value.modifiedCraneHeight = res.data.modifiedCraneHeight ? res.data.modifiedCraneHeight : form.value.modifiedCraneHeight;
    form.value.initialCraneHeight = res.data.initialCraneHeight ? res.data.initialCraneHeight : form.value.initialCraneHeight;
    form.value.installationUnitName = res.data.installationUnitName ? res.data.installationUnitName : form.value.installationUnitName;
    form.value.installationUnitQualification = res.data.installationUnitQualification ? res.data.installationUnitQualification : form.value.installationUnitQualification;
    form.value.safetyProductionPermit = res.data.safetyProductionPermit ? res.data.safetyProductionPermit : form.value.safetyProductionPermit;
  }
};
// 控制项目列表弹框的组件自定义事件
const isShowModelChange = (val: boolean) => {
  isShowModel.value = val;
};
// 安拆任务列表弹框组件自定义事件
const emitSafeDetail = () => {
  openTaskId.value = undefined;
}
onMounted(() => {
  getProjectSelectData()
  getList();
});
</script>
<style lang="scss" scoped>
.prodetail {
  border-bottom: 1px solid rgba(64, 158, 255, 0);
}

.prodetail:hover {
  color: #409eff;
  border-bottom: 1px solid rgb(64, 158, 255);
}

.basicinfo {
  display: flex;
  align-items: center;

  .ercode {
    text-align: center;
    margin-right: 40px;
  }

  .basicinfo-left {
    flex: 1;
  }
}

.basicinfotitle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .gang {
    height: 15px;
    width: 3px;
    background-color: #409eff;
    border-radius: 6px;
    margin-right: 5px;
  }

  .basicinfoname {
    font-weight: 600;
  }
}
</style>