import request from '@/utils/request';

/**
 * 地区
 */

// 获得地区树
export const getAreaTree = async () => {
  return await request({ url: '/system/area/tree', method: 'get'})
}

// 获得地区树
export const getAreaTreeGs = async () => {
  return await request({ url: '/system/area/treeGs', method: 'get'})
}

// 格式化地区ID
export const formatAreaId = async (areaId: string | number) => {
  return await request({ url: '/system/area/formatId', method: 'get', params: { areaId }})
}
