<template>
  <div style="height: 100%; width: 100%" v-loading.fullscreen.lock="fullscreenLoading" class="map-drill-container">
    <div @contextmenu.prevent ref="chartRef" class="map-chart"></div>
    <div v-if="breadcrumbs.length > 0" class="drill-navigation">
      <span>当前位置：</span>
      <template v-for="(item, index) in breadcrumbs" :key="index">
        <a v-if="index >= currentindex" @click="drillTo(index)"> {{ item.name }} </a>
      </template>
      <!-- <a v-for="(item, index) in breadcrumbs" :key="index" @click="drillTo(index)">
        {{ item.name }}
    <span v-if="index < breadcrumbss.length - 1"> > </span>
      </a> -->
    </div>
    <!-- <div v-if="breadcrumbs.length > 0" class="drill-navigation">
      <span>当前位置：</span>
      <a v-for="(item, index) in breadcrumbs" :key="index" @click="drillTo(index)">
        {{ item.name }}
        <span v-if="index < breadcrumbs.length - 1"> > </span>
      </a>
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, defineProps, watchEffect, watch } from 'vue';
import * as echarts from 'echarts';
import { ElMessageBox } from 'element-plus';
import { getlist, getItemNum, getDangerNum, getProItemLis, getYearItemNum, getAreaItemNum } from '@/api/daping/index.js';

import { useRouter, useRoute } from 'vue-router';
import { any } from 'vue-types';
const route = useRoute();
const router = useRouter();
const emit = defineEmits(['getcode']);
const props = defineProps({
  maplat: {
    type: Array
  },
  typedata: {
    type: String
  },
  listpyetdata: {
    type: String
  }
});

const chartRef = ref(null);
let chartInstance = null;

const breadcrumbs = ref([{ name: '全国', adcode: '100000', level: 'country' }]);
const currentindex = ref();
const fullscreenLoading = ref(false);

// 城市标记点数据
let cityMarkers = ref([
  // {
  //   name: '成都市222',
  //   coord: [104.06, 30.67], // 经度, 纬度
  //   // info: `
  //   //   <strong>成都市</strong><br/>
  //   //   四川省省会<br/>
  //   //   人口: 2093万(2021年)<br/>
  //   //   面积: 14335平方公里<br/>
  //   //   GDP: 1.99万亿元(2021年)<br/>
  //   //   著名景点: 都江堰、青城山、武侯祠、杜甫草堂
  //   // `,
  //   symbol: 'pin',
  //   symbolSize: 40,
  //   itemStyle: {
  //     color: '#FF4500'
  //   }
  //   // label: {
  //   //   show: true,
  //   //   formatter: '成都市',
  //   //   position: 'right',
  //   //   color: '#333',
  //   //   fontSize: 14
  //   // }
  // },
  // {
  //   name: '兰州市333',
  //   coord: [103.73, 36.03],
  //   // info: `
  //   //   <strong>兰州市</strong><br/>
  //   //   甘肃省省会<br/>
  //   //   人口: 438万(2021年)<br/>
  //   //   面积: 13085.6平方公里<br/>
  //   //   GDP: 3231亿元(2021年)<br/>
  //   //   著名景点: 黄河风情线、白塔山、五泉山、兴隆山
  //   // `,
  //   symbol: 'pin',
  //   symbolSize: 40,
  //   itemStyle: {
  //     color: '#1E90FF'
  //   }
  //   // label: {
  //   //   show: true,
  //   //   formatter: '兰州市',
  //   //   position: 'right',
  //   //   color: '#333',
  //   //   fontSize: 14
  //   // }
  // }
]);
watch(
  () => props.maplat,
  async (New, Old) => {
    let arr = props.maplat.map((item) => ({
      name: item.project_name,
      coord: item.lola,
      info: item.info, // 简单示例，可根据需要扩展
      url: item.url,
      symbol: 'pin',
      symbolSize: 30,
      itemStyle: {
        color: '#F56C6C' // 统一颜色或根据项目类型设置不同颜色
      }
    }));
    cityMarkers.value = [];
    cityMarkers.value = arr;
    // 如果图表已初始化，需要重新渲染
    if (chartInstance) {
      const currentLevel = breadcrumbs.value[breadcrumbs.value.length - 1];
      let mapJson;
      let mapName;
      try {
        if (currentLevel.level === 'country') {
          const response = await fetch('https://ms-static.gsjtsz.cn/allmap/geo/GeoMapData_CN-master/china.json');
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
          mapJson = await response.json();
          mapName = 'china';
        } else {
          mapJson = await loadGeoJson(currentLevel);
          mapName = `${currentLevel.level}_${currentLevel.adcode}`;
        }

        echarts.registerMap(mapName, mapJson);

        const option = {
          backgroundColor: 'transparent',
          title: {
            left: 'center',
            textStyle: { color: '#000' }
          },
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(255, 255, 255, 0.2)', // 半透明背景
            borderWidth: 0,
            borderRadius: 4,
            padding: 10,
            textStyle: {
              color: '#f2e2e2',
              fontSize: 13
            },
            extraCssText: 'backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);', // 毛玻璃效果
            formatter: (params) => {
              if (params.componentType === 'markPoint') {
                return `
                  <div style="
                     border-radius: 4px;
                     ">
                  ${params.data.info}
                     </div>
                  `;
              } else {
                return;
              }
            }
          },
          visualMap: {
            show: false,
            seriesIndex: [0],
            inRange: { color: ['#cdcaf8', '#026aa9'] }
          },
          series: [
            {
              name: currentLevel.name,
              type: 'map',
              map: mapName,
              roam: true,
              zoom: 1.2,
              scaleLimit: { min: 1.2, max: 10 },
              itemStyle: {
                color: 'rgba(24,43,72,0.6)',
                borderWidth: '1'
              },
              emphasis: { label: { show: true } },
              label: { show: true, color: '#000' },
              data: generateRandomData(mapJson),
              markPoint: {
                data: cityMarkers.value
              }
            }
          ]
        };

        chartInstance.setOption(option, true);
      } catch (error) {
        console.error('地图渲染失败:', error);
      }
    }
  },
  { deep: true }
); // 深度监听);
// watchEffect(async () => {
//   if (props.maplat && props.maplat.length) {
//     let arr = props.maplat.map((item) => ({
//       name: item.project_name,
//       coord: item.lola,
//       info: `项目名称: ${item.project_name}`, // 简单示例，可根据需要扩展
//       symbol: 'pin',
//       symbolSize: 40,
//       itemStyle: {
//         color: '#1E90FF' // 统一颜色或根据项目类型设置不同颜色
//       },
//       label: {
//         show: true,
//         formatter: item.project_name,
//         position: 'right',
//         color: '#333',
//         fontSize: 14
//       }
//     }));
//     // cityMarkers.value = arr;
//     console.log('arr', arr);
//     // 如果图表已初始化，需要重新渲染
//     if (chartInstance) {
//       await renderMap();
//     }
//   }
// });
// // 初始化图表
// const initChart = async () => {
//   let type = 1;
//   if (type == 1) {
//     breadcrumbs.value = [{ name: '中华人民共和国', adcode: '100000', level: 'country' }];
//   } else if (type == 2) {
//     breadcrumbs.value = [
//       { name: '中华人民共和国', adcode: '100000', level: 'country' },
//       { name: '甘肃省', adcode: '620000', level: 'province' }
//       //{ name: '四川省', adcode: '510000', level: 'province' }
//     ];
//   } else if (type == 3) {
//     breadcrumbs.value = [
//       { name: '中华人民共和国', adcode: '100000', level: 'country' },
//       { name: '甘肃省', adcode: '620000', level: 'province' },
//       { name: '兰州市', adcode: '620100', level: 'city' }
//       //{ name: '四川省', adcode: '510000', level: 'province' },
//       //{ name: '成都市', adcode: '510100', level: 'city' }
//     ];
//   } else if (type == 4) {
//     breadcrumbs.value = [
//       { name: '中华人民共和国', adcode: '100000', level: 'country' },
//       { name: '甘肃省', adcode: '620000', level: 'province' },
//       { name: '兰州市', adcode: '620100', level: 'city' },
//       { name: '城关区', adcode: '620102', level: 'district' }
//       // { name: '四川省', adcode: '510000', level: 'province' },
//       // { name: '成都市', adcode: '510100', level: 'city' }
//     ];
//   }
//   currentindex.value = breadcrumbs.value.length - 1;

//   if (chartInstance) {
//     chartInstance.dispose();
//   }

//   chartInstance = echarts.init(chartRef.value);
//   // 如果是部

//   renderMap();
//   // 绑定点击事件
//   chartInstance.on('click', handleMapClick);
//   window.addEventListener('resize', handleResize);
//   chartInstance.on('contextmenu', handleRightClick);
//   // cityweather();
// };
// 初始化图表
const initChart = async () => {
  // breadcrumbs.value = [{ name: '中华人民共和国', adcode: '100000', level: 'country' }];
  breadcrumbs.value = [
    { name: '中华人民共和国', adcode: '100000', level: 'country' },
    { name: '甘肃省', adcode: '620000', level: 'province' }
    //{ name: '四川省', adcode: '510000', level: 'province' }
  ];
  currentindex.value = breadcrumbs.value.length - 1;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value);
  await renderMap();

  // 绑定事件
  chartInstance.on('click', handleMapClick);
  console.log('chartInstance', chartInstance);
  chartInstance.on('contextmenu', handleRightClick);
  window.addEventListener('resize', handleResize);
};

// 渲染地图
const renderMap = async () => {
  const currentLevel = breadcrumbs.value[breadcrumbs.value.length - 1];
  let mapJson;
  let mapName;

  if (currentLevel.level === 'country') {
    const response = await fetch('https://ms-static.gsjtsz.cn/allmap/geo/GeoMapData_CN-master/china.json');
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    mapJson = await response.json();
    mapName = 'china';
  } else if (currentLevel.level == 'district') {
    return;
  } else {
    mapJson = await loadGeoJson(currentLevel);
    mapName = `${currentLevel.level}_${currentLevel.adcode}`;
  }
  echarts.registerMap(mapName, mapJson);
  const option = {
    backgroundColor: 'transparent',
    title: {
      left: 'center',
      textStyle: { color: '#000' }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        // 优先显示标记点信息
        if (params.componentType === 'markPoint') {
          return params.data.info;
        } else {
          return;
        }
      }
    },
    visualMap: {
      show: false,
      seriesIndex: [0],
      inRange: { color: ['#cdcaf8', '#026aa9'] }
    },
    series: [
      {
        name: currentLevel.name,
        type: 'map',
        map: mapName,
        roam: true,
        zoom: 1.2,
        scaleLimit: { min: 1.2, max: 10 },
        itemStyle: {
          color: 'rgba(24,43,72,0.6)',
          borderWidth: '1'
        },
        emphasis: { label: { show: true } },
        label: { show: true, color: '#000' },
        data: generateRandomData(mapJson),
        markPoint: {
          data: cityMarkers.value.map((marker) => ({
            ...marker,
            name: marker.name,
            coord: marker.coord,
            symbol: 'pin',
            symbolSize: 40,
            itemStyle: {
              color: '#1E90FF'
            }
          }))
        }
      }
    ]
  };
  chartInstance.setOption(option, true);
  emit('getcode', currentLevel);
};

// 处理地图点击事件
const handleMapClick = async (params) => {
  if (params.componentType == 'markPoint') {
    if (import.meta.env.DEV) {
      // 开发
      window.open(params.data.url);
      // window.open('http://localhost:' + import.meta.env.VITE_APP_PORT + params.data.url);
    } else {
      window.open(params.data.url);
      // 生产
    }

    return false;
  } else {
    if (breadcrumbs.value[1] && breadcrumbs.value[1].adcode == '110000') {
      return;
    }
    const currentLevel = breadcrumbs.value[breadcrumbs.value.length - 1].level;
    if (currentLevel === 'district') {
      return;
    } // 区级不再下钻
    const adcode = params.data?.adcode || params.data?.properties?.adcode || params.data?.properties?.adcode;
    if (!adcode) return;
    const nextLevel = getNextLevel(currentLevel);

    if (nextLevel == 'district') {
      return;
    } else {
      breadcrumbs.value.push({
        name: params.name,
        adcode,
        level: nextLevel
      });
      await renderMap();
    }
  }
};

// 右键
const handleRightClick = (e) => {
  // console.log('params', e);
};

// 返回上级或跳转到指定层级
const drillTo = async (index) => {
  // console.log(index);
  if (index === breadcrumbs.value.length - 1) return;

  breadcrumbs.value = breadcrumbs.value.slice(0, index + 1);
  await renderMap();
};

// 获取下一层级
const getNextLevel = (currentLevel) => {
  const levelMap = {
    country: 'province',
    province: 'city',
    city: 'district'
  };
  return levelMap[currentLevel] || '';
};
// 获取危大项目
const getproitemlistop = async (obj) => {
  const res = await getProItemLis(obj).catch((e) => {});
  return res;
};
// const loadGeoJson = async (levelInfo) => {
//   let module;

//   try {
//     // 校验 level 是否合法
//     const validLevels = ['province', 'city', 'district'];
//     if (!validLevels.includes(levelInfo.level)) {
//       throw new Error(`Invalid level: ${levelInfo.level}`);
//     }

//     // 构建 URL
//     let url;
//     if (levelInfo.level === 'province') {
//       url = `https://ms-static.gsjtsz.cn/allmap/geo/GeoMapData_CN-master/province/${levelInfo.adcode}.json`;
//     } else if (levelInfo.level === 'city') {
//       if (breadcrumbs.value.length < 2) {
//         throw new Error('Breadcrumb data is insufficient for city level');
//       }
//       const parentAdcode = breadcrumbs.value[breadcrumbs.value.length - 2].adcode;
//       url = `https://ms-static.gsjtsz.cn/allmap/geo/GeoMapData_CN-master/citys/shi/${parentAdcode}/${levelInfo.adcode}.json`;
//     } else if (levelInfo.level === 'district') {
//       if (breadcrumbs.value.length < 3) {
//         throw new Error('Breadcrumb data is insufficient for district level');
//       }
//       const grandParentAdcode = breadcrumbs.value[breadcrumbs.value.length - 3].adcode;
//       const parentAdcode = breadcrumbs.value[breadcrumbs.value.length - 2].adcode;
//       url = `https://ms-static.gsjtsz.cn/allmap/geo/GeoMapData_CN-master/district/${grandParentAdcode}/${parentAdcode}/${levelInfo.adcode}.json`;
//     }

//     const response = await fetch(url);

//     if (!response.ok) {
//       breadcrumbs.value.pop();
//       console.log('333333', response);
//       return;
//     }

//     module = await response.json(); // 已确认是有效 JSON
//     return module;
//   } catch (error) {
//     console.error('加载地图数据失败:', error);
//     breadcrumbs.value.pop(); // 统一清理 breadcrumb
//     throw error;
//   }
// };
// 动态加载GeoJSON数据
const loadGeoJson = async (levelInfo) => {
  let module;
  // const res = await getproitemlistop({
  //   areaCode: levelInfo.adcode,
  //   itemsType: props.typedata,
  //   dangerName: props.listpyetdata
  // });
  // if (res.data.length == 0) {
  //   ElMessageBox.alert('数据正在建设中', '提示', {
  //     // if you want to disable its autofocus
  //     type: 'warning',
  //     confirmButtonText: 'OK'
  //   });
  //   breadcrumbs.value.pop();
  //   return;
  // }
  // console.log('breadcrumbs', breadcrumbs.value);
  try {
    let url;
    if (levelInfo.level == 'province') {
      url = `https://ms-static.gsjtsz.cn/allmap/geo/GeoMapData_CN-master/province/${levelInfo.adcode}.json`;
    } else if (levelInfo.level == 'city') {
      url = `https://ms-static.gsjtsz.cn/allmap/geo/GeoMapData_CN-master/citys/shi/${breadcrumbs.value[breadcrumbs.value.length - 2].adcode}/${levelInfo.adcode}.json`;
    } else if (levelInfo.level == 'district') {
      return;
      url = `https://ms-static.gsjtsz.cn/allmap/geo/GeoMapData_CN-master/district/${breadcrumbs.value[breadcrumbs.value.length - 3].adcode}/${breadcrumbs.value[breadcrumbs.value.length - 2].adcode}/${levelInfo.adcode}.json`;
    }
    const response = await fetch(url).catch((e) => {
      console.log('暂无下钻信息2222', e);
    });
    // if (!response.ok) {
    //   throw new Error(`HTTP error! status: ${response.status} while fetching ${url}`);
    // }
    if (response.status == 200) {
      module = await response.json();
      return module; // 直接返回获取的JSON数据，不需要访问default属性
    } else {
      breadcrumbs.value.pop();
      console.log('333333', response);
      return;
    }
  } catch (error) {
    console.error('加载地图数据失败:', error);
    throw error;
  }
};

// 生成随机数据（演示用）
const generateRandomData = (geoJson) => {
  return geoJson.features.map((feature) => {
    return {
      name: feature.properties.name,
      value: Math.round(Math.random() * 100),
      adcode: feature.properties.adcode || feature.id // 优先从 properties 获取
    };
  });
};

// 窗口大小变化时重绘图表
const handleResize = () => {
  chartInstance?.resize();
};
const keyframesdonghua = () => {
  const observer = new MutationObserver((mutations) => {
    const chart = document.querySelector('.map-chart');
    if (chart && !chart.dataset.animated) {
      chart.dataset.animated = 'true';

      // 添加星空背景
      const stars = document.createElement('div');
      stars.style = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      z-index: -1;
    `;
      chart.parentNode.insertBefore(stars, chart);

      // 添加流星动画
      for (let i = 0; i < 5; i++) {
        const meteor = document.createElement('div');
        meteor.style = `
        position: absolute;
        width: 2px;
        height: 2px;
        background: white;
        border-radius: 50%;
        box-shadow: 0 0 10px 1px white;
        animation: meteor ${3 + Math.random() * 9}s linear infinite;
        top: ${Math.random() * 100}%;
        left: ${Math.random() * 100}%;
        opacity: ${Math.random() * 0.8 + 0.2};
      `;
        chart.parentNode.appendChild(meteor);
      }

      // 添加CSS关键帧
      const style = document.createElement('style');
      style.textContent = `
      @keyframes meteor {
        0% {
          transform: translateX(0) translateY(0);
          opacity: 1;
        }
        70% {
          opacity: 1;
        }
        100% {
          transform: translateX(-300px) translateY(300px);
          opacity: 0;
        }
      }
    `;
      document.head.appendChild(style);
    }
  });

  observer.observe(document.body, { childList: true, subtree: true });
};

// onMounted(() => {
//   initChart();
//   keyframesdonghua();
// });

// onBeforeUnmount(() => {
//   window.removeEventListener('resize', handleResize);
//   chartInstance?.dispose();
// });
onMounted(async () => {
  await initChart();
  // keyframesdonghua();
  // 确保在图表渲染完成后绑定事件
  // chartInstance.on('click', 'series.markPoint', handleMarkerClick);
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  chartInstance?.dispose();
});
</script>

<style scoped>
.container {
  width: 100%;
  height: 100%;
}
.map-drill-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-chart {
  width: 100%;
  height: 100%;
}

.drill-navigation {
  position: absolute;
  top: 10px;
  left: 20px;
  /* background: rgba(255, 255, 255, 0.8); */
  padding: 5px 10px;
  border-radius: 4px;
  z-index: 10;
}

.drill-navigation a {
  color: #1890ff;
  cursor: pointer;
  margin: 0 3px;
}

.drill-navigation a:hover {
  text-decoration: underline;
}
.map-drill-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-chart {
  width: 100%;
  height: 100%;
}

.drill-navigation {
  position: absolute;
  top: 10px;
  left: 20px;
  padding: 5px 10px;
  border-radius: 4px;
  z-index: 10;
}

.drill-navigation a {
  color: #1890ff;
  cursor: pointer;
  margin: 0 3px;
}

.drill-navigation a:hover {
  text-decoration: underline;
}

/* 自定义城市信息框样式 */
:deep(.city-info-box) {
  width: 400px;

  .el-message-box__content {
    padding: 20px;
    line-height: 1.8;
  }

  strong {
    color: #1890ff;
    font-size: 18px;
  }
}
</style>
