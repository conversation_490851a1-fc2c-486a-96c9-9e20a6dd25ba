package org.dromara.system.controller.system;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.area.Area;
import org.dromara.common.core.utils.area.AreaUtils;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.vo.AreaNodeRespVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Tag(name = "管理后台 - 地区")
@RestController
@RequestMapping("/system/area")
@Validated
public class AreaController extends BaseController {

    @GetMapping("/tree")
    @Operation(summary = "获得地区树")
    public R<List<AreaNodeRespVO>> getAreaTree() {
        Area area = AreaUtils.getArea(Area.ID_CHINA);
        Assert.notNull(area, "获取不到中国");
        return R.ok(BeanUtil.copyToList(area.getChildren(), AreaNodeRespVO.class));
    }

    @GetMapping("/treeGs")
    @Operation(summary = "获得甘肃省地区树")
    public R<List<AreaNodeRespVO>> getAreaTreeGs() {
        Area area = AreaUtils.getArea(620000);
        Assert.notNull(area, "获取不到甘肃省");
        return R.ok(BeanUtil.copyToList(area.getChildren(), AreaNodeRespVO.class));
    }

    @GetMapping("/formatId")
    @Operation(summary = "格式化地区ID")
    public R<String> formatId(Integer areaId) {
        Area area = AreaUtils.getArea(areaId);
        Assert.notNull(area, "获取不到该地区");
        return R.ok("success", AreaUtils.formatId(areaId, "/"));
    }

}
