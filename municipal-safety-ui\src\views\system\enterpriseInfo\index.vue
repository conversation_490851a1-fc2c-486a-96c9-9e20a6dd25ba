<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="130">
            <el-form-item label="企业名称" prop="enterpriseName">
              <el-input v-model="queryParams.enterpriseName" placeholder="请输入企业名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="社会统一信用代码" prop="unifiedSocialCreditCode">
              <el-input v-model="queryParams.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="企业类型" prop="enterpriseType">
              <el-select v-model="queryParams.enterpriseType" placeholder="请选择企业类型" clearable>
                <el-option v-for="dict in enterprise_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="法定代表人姓名" prop="legalRepresentative">
              <el-input v-model="queryParams.legalRepresentative" placeholder="请输入法定代表人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="企业所在地">
              <el-select
                style="width: 150px; margin-right: 10px"
                v-model="queryParams.registrationRegionProvince"
                placeholder="请选择省份"
                @change="selectChange($event, 'province')"
              >
                <el-option v-for="(item, index) in provinceOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
              </el-select>
              <el-select
                style="width: 150px; margin-right: 10px"
                v-model="queryParams.registrationRegionCity"
                placeholder="请选择市"
                @change="selectChange($event, 'city')"
              >
                <el-option v-for="(item, index) in cityOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
              </el-select>
              <el-select style="width: 150px" v-model="form.registrationRegionArea" placeholder="请选择区">
                <el-option v-for="(item, index) in areaOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:enterpriseInfo:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:enterpriseInfo:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:enterpriseInfo:remove']"
              >删除</el-button
            >
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:enterpriseInfo:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-change="handleClick">
        <el-tab-pane label="待审核" name="0"></el-tab-pane>
        <el-tab-pane label="通过" name="1"> </el-tab-pane>
        <el-tab-pane label="不通过" name="2"></el-tab-pane>
      </el-tabs>

      <el-table v-loading="loading" :data="enterpriseInfoList" @selection-change="handleSelectionChange" show-overflow-tooltip>
        <el-table-column type="selection" align="center" />
        <el-table-column label="企业名称" align="center" prop="enterpriseName" />
        <el-table-column label="统一社会信用代码" align="center" prop="unifiedSocialCreditCode" />
        <el-table-column label="企业类型" align="center" prop="enterpriseType">
          <template #default="scope">
            <dict-tag :options="enterprise_type" :value="scope.row.enterpriseType" />
          </template>
        </el-table-column>
        <el-table-column label="企业所在地" align="center">
          <template #default="scope">
            <span> {{ scope.row.provinceName }}{{ scope.row.cityName }}{{ scope.row.areaName }} </span>
          </template>
        </el-table-column>
        <el-table-column label="企业地址" align="center" prop="businessAddress" />
        <el-table-column label="法定代表人姓名" align="center" prop="legalRepresentative" />
        <el-table-column label="法人联系电话" align="center" prop="officePhone" />
        <el-table-column label="注册日期" align="center" prop="registrationDate">
          <template #default="scope">
            <span>{{ parseTime(scope.row.registrationDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="150" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:enterpriseInfo:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:enterpriseInfo:remove']"></el-button>
            </el-tooltip>
            <el-tooltip content="审核" placement="top" v-if="activeName == '0'">
              <el-button link type="primary" icon="EditPen" @click="handleexamine(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="Tickets" @click="handledetail(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改企业信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1100px" append-to-body>
      <el-form ref="enterpriseInfoFormRef" :inline="true" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="企业名称" prop="enterpriseName">
          <el-input v-model="form.enterpriseName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
          <el-input v-model="form.unifiedSocialCreditCode" placeholder="请输入统一社会信用代码" />
        </el-form-item>
        <el-form-item label="企业类型" prop="enterpriseType">
          <el-select v-model="form.enterpriseType" placeholder="请选择企业类型" multiple collapse-tags collapse-tags-tooltip>
            <el-option v-for="dict in enterprise_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业地址" prop="businessAddress">
          <el-input v-model="form.businessAddress" placeholder="请输入企业地址" />
        </el-form-item>
        <el-form-item label="法定代表人" prop="legalRepresentative">
          <el-input v-model="form.legalRepresentative" placeholder="请输入法定代表人" />
        </el-form-item>
        <el-form-item label="法人联系电话" prop="officePhone">
          <el-input v-model="form.officePhone" placeholder="请输入法人联系电话" />
        </el-form-item>
        <el-form-item label="注册日期" style="width: 100%" prop="registrationDate">
          <el-date-picker
            style="width: 240px"
            clearable
            v-model="form.registrationDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择注册日期"
          >
          </el-date-picker>
        </el-form-item>
        <div style="width: 100%; display: flex">
          <div style="width: 180px; text-align: right; font-size: 14px; color: #606266; font-weight: 600">
            <span style="color: #f56c6c; margin-right: 5px">*</span>选择企业所在地区：
          </div>
          <el-form-item label="" label-width="0px" style="width: 150px" prop="registrationRegionProvince">
            <el-select v-model="form.registrationRegionProvince" placeholder="请选择省份" @change="selectChange($event, 'province')">
              <el-option v-for="(item, index) in provinceOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" label-width="0px" style="width: 150px" prop="registrationRegionCity">
            <el-select v-model="form.registrationRegionCity" placeholder="请选择市" @change="selectChange($event, 'city')">
              <el-option v-for="(item, index) in cityOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" label-width="0px" style="width: 150px" prop="registrationRegionArea">
            <el-select v-model="form.registrationRegionArea" placeholder="请选择区">
              <el-option v-for="(item, index) in areaOptions" :key="index" :label="item.divisionName" :value="item.divisionCode"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="营业执照" prop="businessLicensePath">
          <image-upload @update:modelValue="handleUrl" v-model="form.businessLicensePath" :limit="1" />
        </el-form-item>
        <el-form-item label="法人代表身份证扫描件" prop="legalIdCardPath">
          <image-upload v-model="form.legalIdCardPath" :limit="1" />
        </el-form-item>
        <el-form-item label="资质证书文件" prop="qualificationCertificatePath">
          <image-upload v-model="form.qualificationCertificatePath" :limit="1" />
        </el-form-item>
        <el-form-item label="安全生产许可证" prop="safetyProductionLicensePath">
          <image-upload v-model="form.safetyProductionLicensePath" :limit="1" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核弹窗 -->
    <el-dialog title="审核" v-model="isexamine" width="500px" append-to-body>
      <!-- 表单内容：单选框1通过 2不通过 -->
      <el-form :model="examineform" :rules="rulesexamine" label-width="80px">
        <el-form-item
          label="审核结果"
          :rules="{
            required: true,
            message: '请选择审核结果',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="examineform.enterpriseStatus">
            <el-radio :value="1">通过</el-radio>
            <el-radio :value="2">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 审核原因 -->
        <el-form-item label="审核原因">
          <!-- 文本域 -->
          <el-input type="textarea" v-model="examineform.enterpriseReason" placeholder="请输入审核原因" />
        </el-form-item>
      </el-form>
      <!-- 确认和取消按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="examinesubmit">确 定</el-button>
          <el-button @click="examinecancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog v-model="isdetail" title="详情" width="60vw">
      <el-descriptions class="margin-top" :column="2" border v-if="detailinfo">
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">企业名称</div>
          </template>
          {{ detailinfo.enterpriseName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">统一社会信用代码</div>
          </template>
          {{ detailinfo.unifiedSocialCreditCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">法定代表人姓名</div>
          </template>
          {{ detailinfo.legalRepresentative }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">法人联系电话</div>
          </template>
          {{ detailinfo.officePhone }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">企业所在地</div>
          </template>
          {{ detailinfo.provinceName }}{{ detailinfo.cityName }}{{ detailinfo.areaName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">企业地址</div>
          </template>
          {{ detailinfo.businessAddress }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">注册日期</div>
          </template>
          {{ detailinfo.registrationDate }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">企业类型</div>
          </template>
          <dict-tag :options="enterprise_type" :value="detailinfo.enterpriseType" />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">营业执照</div>
          </template>
          <el-image
            style="width: 100px; height: 100px"
            :src="list"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[list]"
            show-progress
            :initial-index="4"
            fit="cover"
          />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">法人代表身份证扫描件</div>
          </template>
          <el-image
            style="width: 100px; height: 100px"
            :src="list1"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[list1]"
            show-progress
            :initial-index="4"
            fit="cover"
          />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">资质证书文件</div>
          </template>
          <el-image
            style="width: 100px; height: 100px"
            :src="list2"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[list2]"
            show-progress
            :initial-index="4"
            fit="cover"
          />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">安全生产许可证</div>
          </template>
          <el-image
            style="width: 100px; height: 100px"
            :src="list3"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[list3]"
            show-progress
            :initial-index="4"
            fit="cover"
          />
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isdetail = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EnterpriseInfo" lang="ts">
import {
  listEnterpriseInfo,
  getEnterpriseInfo,
  delEnterpriseInfo,
  addEnterpriseInfo,
  updateEnterpriseInfo,
  auditEnterpriseInfo
} from '@/api/system/enterpriseInfo';
import { EnterpriseInfoVO, EnterpriseInfoQuery, EnterpriseInfoForm } from '@/api/system/enterpriseInfo/types';
import { ExpertVO, ExpertQuery, ExpertForm } from '@/api/expert/expert/types';
import { getProvinceList, getCityList, getAreaList } from '@/api/expert/expert';
import type { FormRules } from 'element-plus';
import { listByIds } from '@/api/system/oss';
import { ElLoading } from 'element-plus';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const enterpriseInfoList = ref<EnterpriseInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const { enterprise_type } = toRefs<any>(proxy?.useDict('enterprise_type'));
const activeName = ref('0');

const queryFormRef = ref<ElFormInstance>();
const enterpriseInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: EnterpriseInfoForm = {
  enterpriseId: undefined,
  enterpriseName: undefined,
  unifiedSocialCreditCode: undefined,
  enterpriseType: [],
  businessAddress: undefined,
  legalRepresentative: undefined,
  registrationRegionProvince: undefined,
  registrationRegionCity: undefined,
  registrationRegionArea: undefined,
  registrationDate: undefined,
  officePhone: undefined,
  businessLicensePath: undefined,
  legalIdCardPath: undefined,
  qualificationCertificatePath: undefined,
  safetyProductionLicensePath: undefined,
  userId: undefined,
  deptId: undefined
};
const data = reactive<PageData<EnterpriseInfoForm, EnterpriseInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    enterpriseName: undefined,
    unifiedSocialCreditCode: undefined,
    enterpriseType: undefined,
    businessAddress: undefined,
    legalRepresentative: undefined,
    registrationRegionProvince: undefined,
    registrationRegionCity: undefined,
    registrationRegionArea: undefined,
    registrationDate: undefined,
    officePhone: undefined,
    businessLicensePath: undefined,
    legalIdCardPath: undefined,
    qualificationCertificatePath: undefined,
    safetyProductionLicensePath: undefined,
    userId: undefined,
    deptId: undefined,
    enterpriseStatus: 0,
    params: {}
  },
  rules: {
    enterpriseName: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
    unifiedSocialCreditCode: [{ required: true, message: '统一社会信用代码不能为空', trigger: 'blur' }],
    enterpriseType: [{ required: true, message: '企业类型不能为空', trigger: 'change' }],
    legalRepresentative: [{ required: true, message: '法定代表人不能为空', trigger: 'blur' }],
    registrationRegionProvince: [{ required: true, message: '省不能为空', trigger: 'blur' }],
    registrationRegionCity: [{ required: true, message: '市不能为空', trigger: 'blur' }],
    registrationRegionArea: [{ required: true, message: '区不能为空', trigger: 'blur' }],
    registrationDate: [{ required: true, message: '注册日期不能为空', trigger: 'blur' }],
    officePhone: [{ required: true, message: '法人联系电话不能为空', trigger: 'blur' }]
  }
});
interface Examineform {
  enterpriseStatus: string | number;
  enterpriseId: string | number;
  enterpriseReason: string;
}

const rulesexamine = reactive<FormRules<Examineform>>({
  enterpriseStatus: [{ required: true, message: '审核状态不能为空', trigger: 'change' }]
});

const provinceOptions = ref<ExpertVO[]>([]); // 省份选项
const cityOptions = ref<ExpertVO[]>([]); // 城市选项
const areaOptions = ref<ExpertVO[]>([]); // 区选项

const { queryParams, form, rules } = toRefs(data);

const handleClick = (tab: any) => {
  queryParams.value.enterpriseStatus = activeName.value;
  getList();
};
const handleUrl = (url: string) => {};
// 获取省份列表信息
const getProvince = async () => {
  const res = await getProvinceList();
  if (res.code === 200) {
    provinceOptions.value = res.data;
  }
};
// 获取市列表信息
const getCity = async (divisionCode: string) => {
  const res = await getCityList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新城市选项等
    cityOptions.value = res.data;
  }
};
// 获取区列表信息
const getArea = async (divisionCode: string) => {
  const res = await getAreaList(divisionCode);
  if (res.code === 200) {
    // 处理返回的数据，例如更新区选项等
    areaOptions.value = res.data;
  }
};
// 选择省份
const selectChange = (divisionCode: string, flag: string) => {
  if (flag === 'province') {
    getCity(divisionCode);
  } else if (flag === 'city') {
    getArea(divisionCode);
  }
};

/** 查询企业信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listEnterpriseInfo(queryParams.value);
  enterpriseInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  enterpriseInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  // 将市区数据也重置
  cityOptions.value = [];
  areaOptions.value = [];
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: EnterpriseInfoVO[]) => {
  ids.value = selection.map((item) => item.enterpriseId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加企业信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: EnterpriseInfoVO) => {
  reset();
  const _enterpriseId = row?.enterpriseId || ids.value[0];
  const res = await getEnterpriseInfo(_enterpriseId);
  Object.assign(form.value, res.data);

  // 处理企业类型：将逗号分割的字符串转换为数组
  if (form.value.enterpriseType && typeof form.value.enterpriseType === 'string') {
    form.value.enterpriseType = form.value.enterpriseType.split(',');
  }

  // 判断省份
  if (row.registrationRegionProvince) {
    // 获取根据省份代码获取市和区列表
    getCity(row.registrationRegionProvince);
    getArea(row.registrationRegionCity);
  }
  dialog.visible = true;
  dialog.title = '修改企业信息';
};

/** 提交按钮 */
const submitForm = () => {
  enterpriseInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;

      // 创建提交数据的副本
      const submitData = { ...form.value };

      // 处理企业类型：将数组转换为逗号分割的字符串
      if (Array.isArray(submitData.enterpriseType)) {
        submitData.enterpriseType = submitData.enterpriseType.join(',');
      }

      if (submitData.enterpriseId) {
        await updateEnterpriseInfo(submitData).finally(() => (buttonLoading.value = false));
      } else {
        await addEnterpriseInfo(submitData).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: EnterpriseInfoVO) => {
  const _enterpriseIds = row?.enterpriseId || ids.value;
  await proxy?.$modal.confirm('是否确认删除企业信息编号为"' + _enterpriseIds + '"的数据项？').finally(() => (loading.value = false));
  await delEnterpriseInfo(_enterpriseIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};
let examineform = ref({
  enterpriseStatus: 2,
  enterpriseId: null,
  enterpriseReason: null
});
let isexamine = ref(false);
let isdetail = ref(false);
// 审核按钮
const handleexamine = async (row?: EnterpriseInfoVO) => {
  examineform.value.enterpriseId = row?.enterpriseId;
  // 弹窗
  isexamine.value = true;
};
let detailinfo = ref({
  enterpriseName: null,
  unifiedSocialCreditCode: null,
  enterpriseType: null,
  legalRepresentative: null,
  registrationRegionProvince: null,
  registrationRegionCity: null,
  registrationDate: null,
  officePhone: null,
  businessLicensePath: null,
  legalIdCardPath: null,
  qualificationCertificatePath: null,
  safetyProductionLicensePath: null,
  registrationRegionArea: null,
  businessAddress: null,
  provinceName: null,
  cityName: null,
  areaName: null
});
const list = ref(null);
const list1 = ref(null);
const list2 = ref(null);
const list3 = ref(null);

const handledetail = async (row) => {
  detailinfo.value = row;
  // 营业执照
  listByIds(row.businessLicensePath).then((res) => {
    list.value = res.data[0].url;
  });
  // 法人代表身份证扫描件路径
  listByIds(row.legalIdCardPath).then((res) => {
    list1.value = res.data[0].url;
  });
  // 资质证书文件路径
  listByIds(row.qualificationCertificatePath).then((res) => {
    list2.value = res.data[0].url;
  });
  // 安全生产许可证路径
  listByIds(row.safetyProductionLicensePath).then((res) => {
    list3.value = res.data[0].url;
  });
  // 弹窗
  isdetail.value = true;
};

const examinesubmit = async () => {
  // 表单验证
  let loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  if (examineform.value.enterpriseStatus) {
    // 判断审核状态，不通过必须填写原因
    if (Number(examineform.value.enterpriseStatus) == 2) {
      if (!examineform.value.enterpriseReason) {
        proxy?.$modal.msgError('请填写审核不通过原因');
        return;
      }
    }
    await auditEnterpriseInfo(examineform.value).catch(() => {
      loading.close();
    });
    // 关闭弹窗
    isexamine.value = false;
    await getList();
  } else {
    // 提示请选择审核状态
    proxy?.$modal.msgError('请选择审核状态');
  }
  loading.close();
};

const examinecancel = () => {
  isexamine.value = false;
  examineform.value = {
    enterpriseStatus: 2,
    enterpriseId: null,
    enterpriseReason: null
  };
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/enterpriseInfo/export',
    {
      ...queryParams.value
    },
    `enterpriseInfo_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  getProvince();
  loading.value = true;
});
</script>
