import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { EnterpriseInfoVO, EnterpriseInfoForm, EnterpriseInfoQuery } from '@/api/system/enterpriseInfo/types';

/**
 * 查询企业信息列表
 * @param query
 * @returns {*}
 */

export const listEnterpriseInfo = (query?: EnterpriseInfoQuery): AxiosPromise<EnterpriseInfoVO[]> => {
  return request({
    url: '/system/enterpriseInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询企业信息详细
 * @param enterpriseId
 */
export const getEnterpriseInfo = (enterpriseId: string | number): AxiosPromise<EnterpriseInfoVO> => {
  return request({
    url: '/system/enterpriseInfo/' + enterpriseId,
    method: 'get'
  });
};

/**
 * 新增企业信息
 * @param data
 */
export const addEnterpriseInfo = (data: EnterpriseInfoForm) => {
  return request({
    url: '/system/enterpriseInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改企业信息
 * @param data
 */
export const updateEnterpriseInfo = (data: EnterpriseInfoForm) => {
  return request({
    url: '/system/enterpriseInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除企业信息
 * @param enterpriseId
 */
export const delEnterpriseInfo = (enterpriseId: string | number | Array<string | number>) => {
  return request({
    url: '/system/enterpriseInfo/' + enterpriseId,
    method: 'delete'
  });
};

// 审核企业
export const auditEnterpriseInfo = (data: any) => {
  return request({
    url: '/system/enterpriseInfo/audit',
    method: 'post',
    data: data,
    timeout: 30000 
  });
};

/**
 * 获取列表下拉数据
 */
export const getSearchData = ()=>{
  return request({
    url: '/system/enterpriseInfo/allForSearch',
    method: 'get'
  });
}
