<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90">
            <el-form-item label="证书编号" prop="certificateNumber">
              <el-input v-model="queryParams.certificateNumber" placeholder="请输入使用登记证书编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发证机关" prop="issuer">
              <el-input v-model="queryParams.issuer" placeholder="请输入使用登记证书发证机关" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备类别" prop="equipmentCategory">
              <el-select v-model="queryParams.equipmentCategory" placeholder="请选择设备类别" clearable>
                <el-option v-for="dict in special_equipment_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" placeholder="请输入工程名称" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item label="项目地址" prop="projectAddress">
              <el-input v-model="queryParams.projectAddress" placeholder="请输入工程项目地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目负责人" prop="projectManager">
              <el-input v-model="queryParams.projectManager" placeholder="请输入项目负责人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item v-if="route.path === '/prj/equipment'" label="项目名称" prop="projectId">
              <el-select v-model="queryParams.projectId" filterable placeholder="请选择项目" style="width: 240px" clearable @change="changePro">
                <el-option v-for="item in projectSelectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header v-if="route.params.id">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['special:equipment:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['special:equipment:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['special:equipment:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['special:equipment:export']">导出</el-button>
          </el-col>
          <el-col :span="1.5" v-if="route.params.id">
            <el-button type="success" plain icon="Link" @click="asyncbut" v-hasPermi="['special:equipment:add']">同步</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        show-overflow-tooltip
        :tooltip-formatter="tableRowFormatter"
        :data="equipmentList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="设备类型" align="center" prop="equipmentCategory">
          <template #default="scope">
            <dict-tag :options="special_equipment_type" :value="scope.row.equipmentCategory" />
          </template>
        </el-table-column>
        <el-table-column label="特种设备生产许可证编号" align="center" prop="productionLicense"> </el-table-column>
        <el-table-column label="备案编号" align="center" prop="recordNumber" />
        <el-table-column label="备案机关" align="center" prop="authority" />
        <!-- <el-table-column label="使用单位" align="center" prop="usageUnit" width="160"> </el-table-column> -->

        <el-table-column label="项目名称" :tooltip-formatter="withVNode" align="center" prop="projectName" />
        <!-- <el-table-column label="工程项目地址" align="center" prop="projectAddress" />
        <el-table-column label="项目负责人" align="center" prop="projectManager" /> -->
        <el-table-column label="进场日期" align="center" prop="enterDate">
          <template #default="scope">
            <span>{{ parseTime(scope.row.enterDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="退场日期" align="center" prop="exitDate">
          <template #default="scope">
            <span>{{ parseTime(scope.row.exitDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="存放地点" align="center" prop="location" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center">
              <div v-if="route.params.id" style="margin-right: 10px">
                <el-tooltip content="修改">
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['special:equipment:edit']"></el-button>
                </el-tooltip>
                <el-tooltip content="删除">
                  <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['special:equipment:remove']"></el-button>
                </el-tooltip>
              </div>
              <div>
                <el-tooltip content="实时数据">
                  <el-button link type="primary" icon="Reading" @click="handleRealTimeData(scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip content="详情">
                  <el-button link type="primary" icon="Tickets" @click="handleDetail(scope.row)"></el-button>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改特种设备对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1800px" append-to-body>
      <el-form ref="equipmentFormRef" :model="form" :rules="rules" label-width="280px" class="infobox">
        <el-form-item label="施工许可证编号" prop="constructionPermitNum">
          <el-input v-model="form.constructionPermitNum" placeholder="请输入使用登记证书编号" />
        </el-form-item>
        <el-form-item label="设备现场编号" prop="projectCraneNum">
          <el-input v-model="form.projectCraneNum" placeholder="请输入设备现场编号" />
        </el-form-item>
        <el-form-item label="备案编号" prop="recordNumber">
          <el-input v-model="form.recordNumber" placeholder="请输入备案编号" />
        </el-form-item>
        <el-form-item label="设备名称" prop="equipmentName">
          <el-input v-model="form.equipmentName" placeholder="请输入备案编号" />
        </el-form-item>
        <el-form-item label="规格型号" prop="modelSpec">
          <el-input v-model="form.modelSpec" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="生产厂商" prop="manufacturer">
          <el-input v-model="form.manufacturer" placeholder="请输入生产厂商" />
        </el-form-item>
        <el-form-item label="生产厂商社会统一信用代码" prop="manufacturerCode">
          <el-input v-model="form.manufacturerCode" placeholder="请输入生产厂商" />
        </el-form-item>
        <el-form-item label="出厂编号" prop="factoryNumber">
          <el-input v-model="form.factoryNumber" placeholder="请输入出厂编号" />
        </el-form-item>

        <el-form-item label="出厂日期" prop="factoryDate">
          <el-date-picker clearable v-model="form.factoryDate" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择请输入出厂日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="特种设备生产许可证编号" prop="productionLicense">
          <el-input v-model="form.productionLicense" placeholder="请输入特种设备生产许可证编号" />
        </el-form-item>
        <!-- <el-form-item label="使用年限" prop="useYears">
              <el-input v-model="form.useYears" placeholder="请输入特种设备生产许可证编号" />
            </el-form-item> -->
        <el-form-item label="产权单位" prop="propertyOwner">
          <el-input v-model="form.propertyOwner" placeholder="请输入产权单位" />
        </el-form-item>

        <el-form-item label="产权单位统一社会代码" prop="propertyOwnerCode">
          <el-input v-model="form.propertyOwnerCode" placeholder="请输入产权单位统一社会代码" />
        </el-form-item>

        <el-form-item label="产权单位地址" prop="propertyOwnerAddress">
          <el-input v-model="form.propertyOwnerAddress" placeholder="请输入产权单位地址" />
        </el-form-item>
        <el-form-item label="企业法人" prop="legalPerson">
          <el-input v-model="form.legalPerson" placeholder="请输入企业法人" />
        </el-form-item>
        <el-form-item label="企业法人证件号" prop="legalPersonLicense">
          <el-input v-model="form.legalPersonLicense" placeholder="请输入企业法人证件号" />
        </el-form-item>
        <el-form-item label="联系人" prop="contacts">
          <el-input v-model="form.contacts" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系人电话" prop="contactsPhone">
          <el-input v-model="form.contactsPhone" placeholder="请输入联系人电话" />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input v-model="form.price" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="购买日期" prop="purchaseDate">
          <el-date-picker clearable v-model="form.purchaseDate" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择购买日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备案机关" prop="authority">
          <el-input v-model="form.authority" placeholder="请输入备案机关" />
        </el-form-item>
        <el-form-item label="发证机关代码" prop="authorityCode">
          <el-input v-model="form.authorityCode" placeholder="请输入发证机关代码" />
        </el-form-item>
        <el-form-item label="机械所在地市" prop="locationCity">
          <el-input v-model="form.locationCity" placeholder="请输入机械所在地市" />
        </el-form-item>
        <el-form-item label="机械所在区县" prop="locationCounty">
          <el-input v-model="form.locationCounty" placeholder="请输入机械所在区县" />
        </el-form-item>
        <el-form-item label="机械所在区域" prop="locationArea">
          <el-input v-model="form.locationArea" placeholder="请输入机械所在区域" />
        </el-form-item>

        <el-form-item label="使用登记证书发证机关" prop="issuer">
          <el-input v-model="form.issuer" placeholder="请输入使用登记证书发证机关" />
        </el-form-item>
        <el-form-item label="使用登记证书编号" prop="certificateNumber">
          <el-input v-model="form.certificateNumber" placeholder="请输入使用登记证书编号" />
        </el-form-item>

        <!-- <el-form-item label="工程名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入工程名称" />
        </el-form-item> -->
        <el-form-item label="设备类型" prop="equipmentCategory">
          <el-select v-model="form.equipmentCategory" placeholder="请选择设备类型">
            <el-option v-for="dict in special_equipment_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.equipmentCategory == '1'" label="塔机类型" prop="craneType">
          <el-select v-model="form.craneType" placeholder="请选择塔机类型">
            <el-option v-for="dict in crane_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="工程名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请选择工程" readonly>
            <template #append>
              <el-button-group>
                <el-button @click="selectProjectChange">选择</el-button>
              </el-button-group>
            </template>
          </el-input>
        </el-form-item> -->
        <el-form-item label="工程项目地址" prop="projectAddress">
          <el-input v-model="form.projectAddress" placeholder="请输入工程项目地址" />
        </el-form-item>
        <el-form-item label="使用单位" prop="usageUnit">
          <el-input v-model="form.usageUnit" placeholder="请输入使用单位" />
        </el-form-item>
        <el-form-item label="维保单位" prop="maintenanceUnit">
          <el-input v-model="form.maintenanceUnit" placeholder="请输入维保单位" />
        </el-form-item>
        <el-form-item label="安装单位" prop="installationUnit">
          <el-input v-model="form.installationUnit" placeholder="请输入安装单位" />
        </el-form-item>
        <el-form-item label="检测单位" prop="inspectionUnit">
          <el-input v-model="form.inspectionUnit" placeholder="请输入检测单位" />
        </el-form-item>
        <el-form-item label="项目负责人" prop="projectManager">
          <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
        </el-form-item>
        <el-form-item label="安装日期" prop="installationDate">
          <el-date-picker clearable v-model="form.installationDate" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择安装日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检测日期" prop="inspectionDate">
          <el-date-picker clearable v-model="form.inspectionDate" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择检测日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="进场日期" prop="enterDate">
          <el-date-picker clearable v-model="form.enterDate" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择进场日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="退场日期" prop="exitDate">
          <el-date-picker clearable v-model="form.exitDate" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择退场日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="使用登记证书" prop="useRegistrationCertificate">
          <image-upload v-model="form.useRegistrationCertificate" />
        </el-form-item>
        <el-form-item label="使用登记证书发证日期" prop="issueDate">
          <el-date-picker clearable v-model="form.issueDate" type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择使用登记证书发证日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="存放地点" prop="location">
          <el-input v-model="form.location" placeholder="请输入存放地点" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-divider content-position="center">设备专业参数信息</el-divider>
        <el-form-item :label="deviceInfoData[key]" v-for="(value, key) in deviceInfoData" :key="key" :prop="'form.' + key">
          <el-input class="neirong" v-model="form[key]" type="text" placeholder="请输入内容" />
          <span v-if="key === 'liftingHeight'" class="el-form-item__error">
            <el-tooltip
              content="标准节主弦杆的材料规格和材质式起重机主要结构件指回转支承 取值示例:【∠180x18,Q355B】或【□200x200,Q355B】或【Ф12x
1.0,Q355B】单位mm"
              placement="top"
            >
              <el-icon style="margin-right: 5px; cursor: pointer"><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
          <span v-if="key === 'strengthenSection'" class="el-form-item__error">
            <el-tooltip content="取值示例:1600x1600x3000" placement="top">
              <el-icon style="margin-right: 5px; cursor: pointer"><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
          <span v-if="key === 'standardSection'" class="el-form-item__error">
            <el-tooltip content="取值示例:1600x1600x3000" placement="top">
              <el-icon style="margin-right: 5px; cursor: pointer"><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
          <span v-if="key === 'constructionElevatorSize'" class="el-form-item__error">
            <el-tooltip content="施工升降机主要结构件指吊笼" placement="top">
              <el-icon style="margin-right: 5px; cursor: pointer"><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog v-model="isDetail" title="详情" width="60vw">
      <el-descriptions class="margin-top" :column="2" border v-if="detailInfo">
        <!-- devicebabletnfo -->
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">特种设备使用登记证书</div>
          </template>
          <el-image
            style="width: 100px; height: 100px"
            :src="list"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[list]"
            show-progress
            :initial-index="4"
            fit="cover"
          />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">设备类型</div>
          </template>
          <div v-for="dict in special_equipment_type" :key="dict.value">
            <span v-if="dict.value == detailInfo.equipmentCategory">{{ dict.label }}</span>
          </div>
        </el-descriptions-item>
        <template v-for="(value, key) in devicebabletnfo" :key="key">
          <el-descriptions-item width="350px" label-width="180px" v-if="detailInfo[key]">
            <template #label>
              <div class="cell-item">{{ devicebabletnfo[key] }}</div>
            </template>
            <span>{{ detailInfo[key] }}</span>
          </el-descriptions-item>
        </template>

        <!-- <el-descriptions-item width="350px" label-width="180px">
          <template #label>
            <div class="cell-item">证书编号</div>
          </template>
          {{ detailInfo.certificateNumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">设备编号</div>
          </template>
          {{ detailInfo.devNo }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">特种设备使用登记证书</div>
          </template>
          <el-image
            style="width: 100px; height: 100px"
            :src="list"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[list]"
            show-progress
            :initial-index="4"
            fit="cover"
          />
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">设备类别</div>
          </template>
          <div v-for="dict in special_equipment_type" :key="dict.value">
            <span v-if="dict.value == detailInfo.equipmentCategory">{{ dict.label }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">规格型号</div>
          </template>
          {{ detailInfo.modelSpec }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">出厂编号</div>
          </template>
          {{ detailInfo.factoryNumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">出厂日期</div>
          </template>
          {{ detailInfo.factoryDate }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">特种设备生产许可证编号</div>
          </template>
          {{ detailInfo.productionLicense }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">使用年限</div>
          </template>
          {{ detailInfo.useYears }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">备案编号</div>
          </template>
          {{ detailInfo.recordNumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">设备类型</div>
          </template>
          {{ detailInfo.equipmentCategory }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">设备名称</div>
          </template>
          {{ detailInfo.equipmentName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">生产厂商社会统一信用代码</div>
          </template>
          {{ detailInfo.manufacturerCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">生产厂商</div>
          </template>
          {{ detailInfo.manufacturer }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">制造单位</div>
          </template>
          {{ detailInfo.manufacturer }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">产权单位</div>
          </template>
          {{ detailInfo.propertyOwner }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">产权单位统一社会代码</div>
          </template>
          {{ detailInfo.propertyOwnerCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">产权单位地址</div>
          </template>
          {{ detailInfo.propertyOwnerAddress }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">企业法人证件号</div>
          </template>
          {{ detailInfo.legalPersonLicense }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">联系人</div>
          </template>
          {{ detailInfo.contacts }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">价格</div>
          </template>
          {{ detailInfo.price }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">购买日期</div>
          </template>
          {{ detailInfo.purchaseDate }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">备案机关</div>
          </template>
          {{ detailInfo.authority }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">发证机关代码</div>
          </template>
          {{ detailInfo.authorityCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">机械所在地市</div>
          </template>
          {{ detailInfo.locationCity }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">机械所在区县</div>
          </template>
          {{ detailInfo.locationCounty }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">机械所在区域</div>
          </template>
          {{ detailInfo.locationArea }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">联系人电话</div>
          </template>
          {{ detailInfo.contactsPhone }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">企业法人</div>
          </template>
          {{ detailInfo.legalPerson }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">工程名称</div>
          </template>
          {{ detailInfo.projectName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">项目地址</div>
          </template>
          {{ detailInfo.projectAddress }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">施工许可证编号</div>
          </template>
          {{ detailInfo.constructionPermitNum }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">使用单位</div>
          </template>
          {{ detailInfo.usageUnit }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">维保单位</div>
          </template>
          {{ detailInfo.maintenanceUnit }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">项目负责人</div>
          </template>
          {{ detailInfo.projectManager }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">安装单位</div>
          </template>
          {{ detailInfo.installationUnit }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">安装日期</div>
          </template>
          {{ formatBirthdate(detailInfo.installationDate) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">检测单位</div>
          </template>
          {{ detailInfo.inspectionUnit }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">检测日期</div>
          </template>
          {{ formatBirthdate(detailInfo.inspectionDate) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">发证机关</div>
          </template>
          {{ detailInfo.issuer }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">发证日期</div>
          </template>
          {{ formatBirthdate(detailInfo.issueDate) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">使用部位</div>
          </template>
          {{ detailInfo.location }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">进场日期</div>
          </template>
          {{ formatBirthdate(detailInfo.enterDate) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">退场日期</div>
          </template>
          {{ formatBirthdate(detailInfo.exitDate) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">备注</div>
          </template>
          {{ detailInfo.remarks }}
        </el-descriptions-item> -->
      </el-descriptions>
      <!-- <el-descriptions class="margin-top" :column="2" border title="塔式起重机">
        <el-descriptions-item width="350px" label-width="180px">
          <template #label>
            <div class="cell-item">额定起重量 T</div>
          </template>
          {{ detailInfo.towerCraneWeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">额定起重力矩（T·M）</div>
          </template>
          {{ detailInfo.weightTorque }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">起重臂长度（M）</div>
          </template>
          {{ detailInfo.weightLength }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">最大工作幅度（M）</div>
          </template>
          {{ detailInfo.workRange }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">最大幅度额定起重量（T）</div>
          </template>
          {{ detailInfo.workRangeWeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">独起升高度（M）</div>
          </template>
          {{ detailInfo.improveHeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">最大起升高度（M）</div>
          </template>
          {{ detailInfo.liftingHeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">主要结构件唯一编号</div>
          </template>
          {{ detailInfo.structureNumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">塔式起重机拟安装最大高度（m）</div>
          </template>
          {{ detailInfo.maxHeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">标准节主要结构件规格</div>
          </template>
          {{ detailInfo.standardSectionSpecifications }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">加强节参数（长×宽×高）mm</div>
          </template>
          {{ detailInfo.strengthenSection }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">标准节参数（长×宽×高）mm</div>
          </template>
          {{ detailInfo.standardSection }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions class="margin-top" :column="2" border title="施工升降机">
        <el-descriptions-item width="350px" label-width="180px">
          <template #label>
            <div class="cell-item">施工升降机用途类型</div>
          </template>
          {{ detailInfo.constructionElevatorPurpose }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">额定载重量(T)</div>
          </template>
          {{ detailInfo.constructionElevatorWeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">电动机总功率（kW）</div>
          </template>
          {{ detailInfo.constructionElevatorPower }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">额定提升速度（m/min）</div>
          </template>
          {{ detailInfo.improveSpeed }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">最大提升高度</div>
          </template>
          {{ detailInfo.constructionElevatorMaxHeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">防坠安全器型号</div>
          </template>
          {{ detailInfo.safetyModel }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">运载装置（吊笼）净空尺寸（长×宽×高）m</div>
          </template>
          {{ detailInfo.constructionElevatorSize }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">主要结构件唯一编号</div>
          </template>
          {{ detailInfo.constructionElevatorStructureNumber }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions class="margin-top" :column="2" border title="物料提升机">
        <el-descriptions-item width="350px" label-width="180px">
          <template #label>
            <div class="cell-item">额定载重量</div>
          </template>
          {{ detailInfo.materialHoistWeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">额定提升速度（m/min）</div>
          </template>
          {{ detailInfo.materialHoistSpeed }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">防坠安全器型号</div>
          </template>
          {{ detailInfo.materialHoistSafetyModel }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">最大起升高度（M）</div>
          </template>
          {{ detailInfo.materialHoistMaxHeight }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">电动机总功率（kW）</div>
          </template>
          {{ detailInfo.materialHoistPower }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">主要结构件唯一编号</div>
          </template>
          {{ detailInfo.materialHoistStructureNumber }}
        </el-descriptions-item>
      </el-descriptions> -->

      <el-descriptions class="margin-top" :column="2" border title="设备信息">
        <template v-for="(value, key) in deviceInfoData" :key="key">
          <el-descriptions-item width="350px" label-width="180px" v-if="detailInfo[key]">
            <template #label>
              <div class="cell-item">{{ deviceInfoData[key] }}</div>
            </template>
            <span>{{ detailInfo[key] }}</span>
          </el-descriptions-item>
        </template>
      </el-descriptions>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isDetail = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 导入监控管理对话框 -->
    <Project :isShowModel="isShowModel" @update:isShowModel="isShowModelChange" @selectionProjectData="handleSelectionProject" />
    <!-- 塔吊的实时数据 -->
    <RealTimeDialog :isShowRealTimeModel="isShowRealTimeModel" :devNo="devNo" @update:isShowRealTimeModel="isShowRealTimeModelChange" />
    <!-- 升降机的实时数据 -->
    <LiftDialog :isShowLiftModel="isShowLiftModel" :devNo="devNo" @update:isShowLiftModel="isShowLiftModelChange" />
    <!-- 卸料平台的实时数据 -->
    <UnloadingDialog :isShowDumpModel="isShowDumpModel" :devNo="devNo" @update:isShowDumpModel="isShowDumpModelChange" />
  </div>
</template>

<script setup name="Equipment" lang="ts">
import { listEquipment, getEquipment, delEquipment, addEquipment, updateEquipment } from '@/api/special/equipment';
import { EquipmentVO, EquipmentQuery, EquipmentForm } from '@/api/special/equipment/types';
import RealTimeDialog from '@/components/RealTimeDialog/index.vue';
import LiftDialog from '@/components/LiftDialog/index.vue';
import UnloadingDialog from '@/components/UnloadingDialog/index.vue';
import { listByIds } from '@/api/system/oss';
import { useRoute } from 'vue-router';
import { get_prj_search_data, getItemList } from '@/api/projects/prj_hazardous_items';
import { ElMessage, ElMessageBox } from 'element-plus';
import { equipmentsync } from '@/api/special/operationPersonnel';
import deviceInfo from './device.js';
import babletnfo from './bableinfo.js';
const deviceInfoData = ref(deviceInfo);
const devicebabletnfo = ref(babletnfo);
const route = useRoute();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { special_equipment_type, crane_type } = toRefs<any>(proxy?.useDict('special_equipment_type', 'crane_type'));

const equipmentList = ref<EquipmentVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const getItemData = ref([]);
const total = ref(0);
const isShowModel = ref(false);
const isShowRealTimeModel = ref(false); // 塔吊的实时数据弹窗
const isShowLiftModel = ref(false); // 升降机的实时数据弹窗
const isShowDumpModel = ref(false); // 卸料平台的实时数据弹窗
const devNo = ref(''); // 设备编号
const queryFormRef = ref<ElFormInstance>();
const equipmentFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
import { h } from 'vue';
import router from '@/router';
const withVNode = (data: TableTooltipData<TableData>) => {
  return h(ElLink, { type: 'primary', href: data.cellValue }, () => h('span', null, data.cellValue));
};
const tableRowFormatter = (data: TableTooltipData<TableData>) => {
  return `${data.cellValue}: table formatter`;
};
const initFormData: EquipmentForm = {
  equipmentId: undefined,
  certificateNumber: undefined,
  issuer: undefined,
  issueDate: undefined,
  useRegistrationCertificate: undefined,
  equipmentCategory: undefined,
  devNo: undefined,
  modelSpec: undefined,
  factoryNumber: undefined,
  recordNumber: undefined,
  manufacturer: undefined,
  propertyOwner: undefined,
  projectName: undefined,
  projectAddress: undefined,
  projectId: undefined,
  itemId: undefined,
  usageUnit: undefined,
  maintenanceUnit: undefined,
  installationUnit: undefined,
  inspectionUnit: undefined,
  projectManager: undefined,
  installationDate: undefined,
  inspectionDate: undefined,
  enterDate: undefined,
  exitDate: undefined,
  location: undefined,
  sopId: undefined,
  remarks: undefined
};
const data = reactive<PageData<EquipmentForm, EquipmentQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    certificateNumber: undefined,
    issuer: undefined,
    issueDate: undefined,
    useRegistrationCertificate: undefined,
    equipmentCategory: undefined,
    modelSpec: undefined,
    factoryNumber: undefined,
    recordNumber: undefined,
    manufacturer: undefined,
    propertyOwner: undefined,
    projectName: undefined,
    projectAddress: undefined,
    projectId: undefined,
    itemId: undefined,
    usageUnit: undefined,
    maintenanceUnit: undefined,
    installationUnit: undefined,
    inspectionUnit: undefined,
    projectManager: undefined,
    installationDate: undefined,
    inspectionDate: undefined,
    enterDate: undefined,
    exitDate: undefined,
    location: undefined,
    sopId: undefined,
    remarks: undefined,
    params: {}
  },
  rules: {
    certificateNumber: [{ required: true, message: '证书编号不能为空', trigger: 'blur' }],
    issuer: [{ required: true, message: '发证机关不能为空', trigger: 'blur' }],
    issueDate: [{ required: true, message: '发证日期不能为空', trigger: 'change' }],
    useRegistrationCertificate: [{ required: true, message: '登记证书ID不能为空', trigger: 'blur' }],
    equipmentCategory: [{ required: true, message: '设备类别不能为空', trigger: 'change' }],
    devNo: [{ required: true, message: '设备编号不能为空', trigger: 'blur' }],
    modelSpec: [{ required: true, message: '规格型号不能为空', trigger: 'blur' }],
    factoryNumber: [{ required: true, message: '出厂编号不能为空', trigger: 'change' }],
    recordNumber: [{ required: true, message: '备案编号不能为空', trigger: 'change' }],
    manufacturer: [{ required: true, message: '制造单位不能为空', trigger: 'blur' }],
    propertyOwner: [{ required: true, message: '产权单位不能为空', trigger: 'blur' }],
    projectName: [{ required: true, message: '工程名称不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
    projectAddress: [{ required: true, message: '工程项目地址不能为空', trigger: 'blur' }],
    usageUnit: [{ required: true, message: '使用单位不能为空', trigger: 'blur' }],
    maintenanceUnit: [{ required: true, message: '维保单位不能为空', trigger: 'blur' }],
    installationUnit: [{ required: true, message: '安装单位不能为空', trigger: 'blur' }],
    inspectionUnit: [{ required: true, message: '检测单位不能为空', trigger: 'blur' }],
    projectManager: [{ required: true, message: '安装单位不能为空', trigger: 'blur' }],
    installationDate: [{ required: true, message: '安装日期不能为空', trigger: 'blur' }],
    inspectionDate: [{ required: true, message: '检测日期不能为空', trigger: 'blur' }],
    enterDate: [{ required: true, message: '进场日期不能为空', trigger: 'blur' }],
    exitDate: [{ required: true, message: '退场日期不能为空', trigger: 'blur' }],
    location: [{ required: true, message: '存放地点不能为空', trigger: 'blur' }],
    remarks: [{ required: true, message: '备注不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
// 详情
let isDetail = ref(false);
let detailInfo = ref({
  equipmentId: null,
  certificateNumber: null,
  issuer: null,
  issueDate: null,
  useRegistrationCertificate: null,
  equipmentCategory: null,
  devNo: null,
  modelSpec: null,
  factoryNumber: null,
  recordNumber: null,
  manufacturer: null,
  propertyOwner: null,
  projectName: null,
  projectAddress: null,
  usageUnit: null,
  maintenanceUnit: null,
  installationUnit: null,
  inspectionUnit: null,
  projectManager: null,
  installationDate: null,
  inspectionDate: null,
  enterDate: null,
  exitDate: null,
  location: null,
  remarks: null,
  weightTorque: 12,
  weightLength: null,
  workRange: 40
});
const projectSelectData = ref([]);

const list = ref(null);
// 查看实时数据的方法
const handleRealTimeData = (row) => {
  devNo.value = row.devNo;
  if (row.equipmentCategory == '1') {
    isShowRealTimeModel.value = true;
  } else if (row.equipmentCategory == '2') {
    isShowLiftModel.value = true;
  } else if (row.equipmentCategory == '9') {
    isShowDumpModel.value = true;
  }
};
// 塔吊实时数据的弹窗的自定义事件
const isShowRealTimeModelChange = (val: boolean) => {
  isShowRealTimeModel.value = val;
};
// 升降机实时数据的弹窗的自定义事件
const isShowLiftModelChange = (val: boolean) => {
  isShowLiftModel.value = val;
};
// 卸料平台实时数据的弹窗的自定义事件
const isShowDumpModelChange = (val: boolean) => {
  isShowDumpModel.value = val;
};
const handleDetail = async (row) => {
  detailInfo.value = row;

  if (row.useRegistrationCertificate) {
    listByIds(row.useRegistrationCertificate).then((res) => {
      list.value = res.data[0].url;
    });
  }
  // 弹窗
  isDetail.value = true;
};
const changePro = async (val) => {
  getItemList(val).then((res) => {
    getItemData.value = res.data;
  });
};
//查询项目选择框数据
const getProjectSelectData = async () => {
  const { data } = await get_prj_search_data();
  projectSelectData.value = data;
};
const isShowModelChange = (val: boolean) => {
  isShowModel.value = val;
};

const handleSelectionProject = (data: { projectId: string; projectName: string }) => {
  console.log('data', data);
  form.value.projectId = data.projectId;
  form.value.projectName = data.projectName;
  isShowModel.value = false;
};
/** 查询特种设备列表 */
const getList = async () => {
  loading.value = true;
  const res = await listEquipment({ ...queryParams.value, projectId: route.params.projectId ? route.params.projectId : queryParams.value.projectId });
  equipmentList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};
const selectProjectChange = () => {
  isShowModel.value = true;
};
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  equipmentFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: EquipmentVO[]) => {
  ids.value = selection.map((item) => item.equipmentId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加特种设备';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: EquipmentVO) => {
  reset();
  const _equipmentId = row?.equipmentId || ids.value[0];
  const res = await getEquipment(_equipmentId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改特种设备';
};

/** 提交按钮 */
const submitForm = () => {
  equipmentFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.equipmentId) {
        await updateEquipment({ ...form.value, projectId: route.query.projectId }).finally(() => (buttonLoading.value = false));
      } else {
        await addEquipment({ ...form.value, projectId: route.query.projectId }).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: EquipmentVO) => {
  const _equipmentIds = row?.equipmentId || ids.value;
  await proxy?.$modal.confirm('是否确认删除特种设备编号为"' + _equipmentIds + '"的数据项？').finally(() => (loading.value = false));
  await delEquipment(_equipmentIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'special/equipment/export',
    {
      ...queryParams.value
    },
    `equipment_${new Date().getTime()}.xlsx`
  );
};
/** 同步按钮操作 */
const asyncbut = () => {
  ElMessageBox.confirm('是否同步?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      // 全局loading
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '数据同步中...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      try {
        const res = await equipmentsync({
          type: 2,
          constructionPermitNo: route.query.constructionPermitNo,
          constructionId: route.query.constructionId,
          projectId: route.query.projectId
        });
        if (res.code == 200) {
          ElMessage.success(res.msg);
          getList();
        } else {
          ElMessage.warning(res.msg);
        }
      } catch (error) {
        ElMessage.error('同步失败');
        console.error('同步错误:', error);
      } finally {
        // 关闭loading
        loadingInstance.close();
      }
    })
    .catch(() => {});
};
onMounted(() => {
  getList();
  getProjectSelectData();
  deviceInfoData.value = deviceInfo;
  devicebabletnfo.value = babletnfo;
});
const formatBirthdate = (birthdate) => {
  if (typeof birthdate !== 'string') return '';
  // 去除前后空白后按空白分割
  const parts = birthdate.trim().split(/\s+/);
  return parts[0];
};
</script>
<style scoped lang="scss">
.infobox {
  display: flex;
  flex-wrap: wrap;
  .el-form-item {
    width: 50%;
    display: flex;
    align-items: center;
  }
}
.neirong {
  width: 80%;
}
</style>
