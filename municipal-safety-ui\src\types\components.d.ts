/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ApprovalRecord: typeof import('./../components/Process/approvalRecord.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    DataSource: typeof import('./../components/dataSource/index.vue')['default']
    DepartmentManagement: typeof import('./../components/DepartmentManagement/index.vue')['default']
    DictTag: typeof import('./../components/DictTag/index.vue')['default']
    DispatchReview: typeof import('./../components/DispatchReview/index.vue')['default']
    Dump_plat: typeof import('./../components/UnloadingDialog/dump_plat.vue')['default']
    Editor: typeof import('./../components/Editor/index.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAutoResizer: typeof import('element-plus/es')['ElAutoResizer']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCalendar: typeof import('element-plus/es')['ElCalendar']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElCountdown: typeof import('element-plus/es')['ElCountdown']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTableV2: typeof import('element-plus/es')['ElTableV2']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTour: typeof import('element-plus/es')['ElTour']
    ElTourStep: typeof import('element-plus/es')['ElTourStep']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ExpertDetail: typeof import('./../components/expertDetail/index.vue')['default']
    Expertmanagement: typeof import('./../components/Expertmanagement/index.vue')['default']
    ExpertReview: typeof import('./../components/ExpertReview/index.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    GdMap: typeof import('./../components/GdMap/index.vue')['default']
    Hamburger: typeof import('./../components/Hamburger/index.vue')['default']
    HazardListEdit: typeof import('./../components/HazardListEdit/index.vue')['default']
    IconSelect: typeof import('./../components/IconSelect/index.vue')['default']
    IEpCaretBottom: typeof import('~icons/ep/caret-bottom')['default']
    IEpCaretTop: typeof import('~icons/ep/caret-top')['default']
    IEpUploadFilled: typeof import('~icons/ep/upload-filled')['default']
    IFrame: typeof import('./../components/iFrame/index.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    ImageUploadRegister: typeof import('./../components/ImageUploadRegister/index.vue')['default']
    Index1: typeof import('./../components/MonitorPlayer/index1.vue')['default']
    Indexcheck: typeof import('./../components/Project/indexcheck.vue')['default']
    LangSelect: typeof import('./../components/LangSelect/index.vue')['default']
    Left: typeof import('./../components/Monitor/left.vue')['default']
    LiftDialog: typeof import('./../components/LiftDialog/index.vue')['default']
    Lifter_real: typeof import('./../components/LiftDialog/real/lifter_real.vue')['default']
    ManualDispatch: typeof import('./../components/ManualDispatch/ManualDispatch.vue')['default']
    Middle: typeof import('./../components/Monitor/middle.vue')['default']
    Monitor: typeof import('./../components/Monitor/index.vue')['default']
    MonitorPlayer: typeof import('./../components/MonitorPlayer/index.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    ParentView: typeof import('./../components/ParentView/index.vue')['default']
    PersonDetail: typeof import('./../components/personDetail/index.vue')['default']
    PLAYcomp: typeof import('./../components/PLAYERcomp/PLAYcomp.vue')['default']
    Prj_hazardous: typeof import('./../components/Project/prj_hazardous.vue')['default']
    Prj_hazardous_items_detail: typeof import('./../components/prj_hazardous_items_detail/index.vue')['default']
    ProcessMeddle: typeof import('./../components/Process/processMeddle.vue')['default']
    Project: typeof import('./../components/Project/index.vue')['default']
    RealTimeDialog: typeof import('./../components/RealTimeDialog/index.vue')['default']
    Register: typeof import('./../components/Register/index.vue')['default']
    RightToolbar: typeof import('./../components/RightToolbar/index.vue')['default']
    RoleSelect: typeof import('./../components/RoleSelect/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RuoYiDoc: typeof import('./../components/RuoYiDoc/index.vue')['default']
    RuoYiGit: typeof import('./../components/RuoYiGit/index.vue')['default']
    Screenfull: typeof import('./../components/Screenfull/index.vue')['default']
    ScreenShot: typeof import('./../components/ScreenShot/index.vue')['default']
    SelectExpert: typeof import('./../components/SelectExpert/index.vue')['default']
    SizeSelect: typeof import('./../components/SizeSelect/index.vue')['default']
    SubmitVerify: typeof import('./../components/Process/submitVerify.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    Tianqiyubao: typeof import('./../components/Tianqiyubao/index.vue')['default']
    TopNav: typeof import('./../components/TopNav/index.vue')['default']
    Tower_real: typeof import('./../components/RealTimeDialog/tower_real.vue')['default']
    TreeSelect: typeof import('./../components/TreeSelect/index.vue')['default']
    UnloadingDialog: typeof import('./../components/UnloadingDialog/index.vue')['default']
    UserSelect: typeof import('./../components/UserSelect/index.vue')['default']
    Video: typeof import('./../components/PLAYERcomp/video.vue')['default']
    VideoPlayer: typeof import('./../components/VideoPlayer.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
